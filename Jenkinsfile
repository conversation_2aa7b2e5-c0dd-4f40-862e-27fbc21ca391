pipeline {
    agent any

    environment {
        TARGET_USER = 'ashadev@127.0.0.1'
        TARGET_DIR = '/home/<USER>/node_server/pos-asha/'
        DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/1204375617710071899/mJoYhxvtHK0KOokOMKRHiz_gI3XuhjfmfPmdQ2kXzc5uKxZCINAKjqao8x2qrpzdZWMq'
    }

    stages {
        stage('Build & Deploy') {
            steps {
                echo 'Pulling latest code and running npm install and build...'
                sh """
                  ssh $TARGET_USER 'source ~/.nvm/nvm.sh && cd $TARGET_DIR && git pull && npm install && npm run build && npm run prod:stop && npm run prod:start'
                """
            }
        }
    }

    post {
        success {
            echo '✅ Build succeeded!'
            sendDiscordNotification("✅ *${env.JOB_NAME}* - Build #${env.BUILD_NUMBER} succeeded! <${env.BUILD_URL}|View Logs>")
        }

        failure {
            echo '❌ Build failed!'
            sendDiscordNotification("❌ *${env.JOB_NAME}* - Build #${env.BUILD_NUMBER} failed! <${env.BUILD_URL}|View Logs>")
        }
    }
}

def sendDiscordNotification(message) {
    def payload = [content: message]
    def json = groovy.json.JsonOutput.toJson(payload)
    sh """curl -X POST -H 'Content-Type: application/json' -d '${json}' '${env.DISCORD_WEBHOOK_URL}'"""
}
