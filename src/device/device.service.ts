import { UpdateBranchDto } from './../branch/dto/update-branch.dto';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateDeviceDto } from './dto/create-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { InjectRepository } from '@nestjs/typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { DataSource, FindOptionsWhere, Not, Repository } from 'typeorm';
import { Device } from './entities/device.entity';
import { Branch } from 'src/branch/entities/branch.entity';

export const DEVICE_PAGINATION_CONFIG: PaginateConfig<Device> = {
  sortableColumns: ['code', 'name'],
  searchableColumns: ['code', 'name'],
  defaultSortBy: [['code', 'ASC']],
};

@Injectable()
export class DeviceService {
  constructor(
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    private dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Device>> {
    return paginate(query, this.deviceRepository, {
      ...DEVICE_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  //Check name and code exist
  async checkDeviceNameAndCodeExist(name: string, code: string, storeId: number, id?: number) {
    const existingDeviceWithName = this.deviceRepository.exists({
      where: {
        name,
        store: {
          id: storeId,
        },
        ...(id ? { id: Not(id) } : {}),
      }
    });

    const existingDeviceWithCode = this.deviceRepository.exists({
      where: {
        code,
        store: {
          id: storeId,
        },
        ...(id ? { id: Not(id) } : {}),
      },
    });

    const [nameExist, codeExist] = await Promise.all([
      existingDeviceWithName,
      existingDeviceWithCode,
    ]);

    if (nameExist) {
      throw new BadRequestException(`Device with name '${name}' already exists`);
    }

    if (codeExist) {
      throw new BadRequestException(`Device with code '${code}' already exists`);
    }
  }

  async create(createDeviceDto: CreateDeviceDto, user: any) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      //check name and code exist
      await this.checkDeviceNameAndCodeExist(
        createDeviceDto.name,
        createDeviceDto.code,
        user?.storeId,
      );

      const device = this.deviceRepository.create({
        ...createDeviceDto,
        description: createDeviceDto.description || null,
        active: createDeviceDto.active,
        store: {
          id: user?.storeId,
        },
      });
      await queryRunner.manager.save(device);
      await queryRunner.commitTransaction();

      return device;
    } catch (err) {
      console.error(err);
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  findAll(user: any) {
    return this.deviceRepository.find({
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async findOne(id: number) {
    const device = await this.deviceRepository.findOne({
      where: { id },
    });

    if (!device) {
      throw new NotFoundException('device not found');
    }

    return device;
  }

  async update(id: number, updateDeviceDto: UpdateDeviceDto, user: any) {
    const device = await this.deviceRepository.findOneBy({ id });
    if (!device) {
      throw new NotFoundException('Device not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      //check name and code exist
      await this.checkDeviceNameAndCodeExist(
        updateDeviceDto.name,
        updateDeviceDto.code,
        user?.storeId,
        id,
      );

      device.description = updateDeviceDto.description;
      device.name = updateDeviceDto.name;
      device.code = updateDeviceDto.code;
      device.active = updateDeviceDto.active;

      await queryRunner.manager.save(device);

      await queryRunner.commitTransaction();

      return device;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const device = await this.deviceRepository.findOneBy({ id });

    if (!device) {
      throw new NotFoundException('device not found');
    }

    await this.deviceRepository.softRemove(device);
  }
}
