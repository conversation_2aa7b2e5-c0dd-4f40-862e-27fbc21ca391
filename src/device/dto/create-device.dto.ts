import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';

export class CreateDeviceDto {
  @ApiProperty()
  @IsNotEmpty({ message: 'Code is required' })
  code: string;

  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'Active is required' })
  @IsBoolean()
  active: boolean;
}
