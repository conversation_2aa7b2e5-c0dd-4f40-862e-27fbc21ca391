import { Module } from '@nestjs/common';
import { DeviceService } from './device.service';
import { DeviceController } from './device.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Device } from './entities/device.entity';
import { BranchModule } from 'src/branch/branch.module';
import { Branch } from 'src/branch/entities/branch.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Device, Branch])],
  controllers: [DeviceController],
  providers: [DeviceService],
})
export class DeviceModule {}
