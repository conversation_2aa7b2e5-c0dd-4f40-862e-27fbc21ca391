import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource } from 'typeorm';
import { Product } from './entities/product.entity';
import { CreateProductAttributeDto } from './dto/create-product-attribute.dto';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductAttributeValue } from './entities/product-attribute-value.entity';

@Injectable({ scope: Scope.REQUEST })
export class ProductRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  get repository() {
    return this.getRepository(Product);
  }

  create() {}

  async createAttribute(
    productId: number,
    attributeDto: CreateProductAttributeDto,
  ) {
    const proAttRepository = this.getRepository(ProductAttribute);
    const proAttValueRepository = this.getRepository(ProductAttributeValue);

    const { attributeValues, ...attribute } = attributeDto;

    const productAttribute = proAttRepository.create({
      ...attribute,
      product: { id: productId },
    });
    await proAttRepository.save(productAttribute);

    const attributeValuesData = attributeValues.map((e) =>
      proAttValueRepository.create({
        ...e,
        productAttribute: { id: productAttribute.id },
      }),
    );

    await proAttValueRepository.save(attributeValuesData);

    return productAttribute;
  }

  async deleteAttribute(productAttributeId: number) {
    const proAttRepository = this.getRepository(ProductAttribute);

    await proAttRepository.softDelete(productAttributeId);
  }

  findAttribute(attributeId: number) {
    return this.getRepository(ProductAttribute).findOneBy({ id: attributeId });
  }
}
