import { Category } from '../../category/entities/category.entity';
import { CustomBaseEntity } from '../../common/entities';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { OrderItem } from '../../order/entities/order-item.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Product } from './product.entity';
import { ProductAttribute } from './product-attribute.entity';

@Entity()
export class ProductAttributeValue extends CustomBaseEntity {
  @Column({ comment: 'Product-attribute-value name' })
  name: string;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Product-attribute-value price',
  })
  price: number;

  @ManyToOne(
    () => ProductAttribute,
    (productAttribute) => productAttribute.productAttributeValues,
    {
      onDelete: 'CASCADE',
    },
  )
  productAttribute: ProductAttribute;

  constructor(partial?: Partial<ProductAttributeValue>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
