import {
  BadRequestException,
  Injectable,
  NotFoundException,
  Delete,
} from '@nestjs/common';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Product } from './entities/product.entity';
import {
  DataSource,
  FindOptionsWhere,
  ILike,
  In,
  Not,
  QueryRunner,
  Repository,
} from 'typeorm';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import {
  CreateProductAttributeDto,
  CreateProductAttributeValueDto,
} from './dto/create-product-attribute.dto';
import { imagePath } from 'src/common/helper';
import { ProductRepository } from './product.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductAttributeValue } from './entities/product-attribute-value.entity';
import * as xlsx from 'xlsx';
import { Workbook } from 'exceljs';
import { createECDH } from 'crypto';
import { Category } from 'src/category/entities/category.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import { Branch } from 'src/branch/entities/branch.entity';
import { DateTime } from 'luxon';
import { Vendor } from 'src/vendor/entities/vendor.entity';

export const PRODUCT_PAGINATION_CONFIG: PaginateConfig<Product> = {
  sortableColumns: [
    'code',
    'name',
    'price',
    'category.name',
    'unit.name',
  ],
  relations: { category: true, unit: true },
  defaultSortBy: [['id', 'ASC']],
  searchableColumns: ['code', 'name'],
  filterableColumns: {
    'category.id': [FilterOperator.EQ],
  },
};

@Injectable()
export class ProductService {
  constructor(
    private dataSource: DataSource,
    private productRepository1: ProductRepository,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(ProductAttribute)
    private productAttributeRepository: Repository<ProductAttribute>,
    @InjectRepository(ProductAttributeValue)
    private productAttributeValueRepository: Repository<ProductAttributeValue>,
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
    @InjectRepository(Unit)
    private unitRepository: Repository<Unit>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
  ) { }

  async create(createProductDto: CreateProductDto, user: any) {
    //check code exist
    const codeExistQuery = this.productRepository.exists({
      where: {
        code: createProductDto.code,
        ...user?.storeId && ({ store: { id: user.storeId } })
      },
    });

    //check barcode exist
    const barcodeExistQuery = this.productRepository.exists({
      where: {
        barcode: createProductDto.barcode,
        ...user?.storeId && ({ store: { id: user.storeId } })
      },
    });

    const [codeExist, barcodeExist] = await Promise.all([
      codeExistQuery,
      barcodeExistQuery,
    ]);

    if (codeExist) {
      throw new BadRequestException('Code already exists');
    }

    if (barcodeExist) {
      throw new BadRequestException('Barcode already exists');
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      const product = this.productRepository.create({
        ...createProductDto,
        category: {
          id: createProductDto.categoryId,
        },
        unit: {
          id: createProductDto.unitId,
        },
        code: createProductDto.code,
        branches: createProductDto.branchIds.map((id) => ({ id })),
        active: createProductDto.active || false,
        // vatStatus: createProductDto.vatStatus || false,
        // remarkStatus: createProductDto.remarkStatus || false,
        barcode: createProductDto.barcode || null,
        vendors: createProductDto.vendorId.map((id) => ({ id })),
        store: {
          id: user?.storeId,
        },
      });

      await queryRunner.manager.save(product);

      // for (const attributeDto of createProductDto?.attributes) {
      //   const attribute = this.productAttributeRepository.create({
      //     ...attributeDto,
      //     product,
      //   });

      //   await queryRunner.manager.save(attribute);

      //   const attributeValues = [];
      //   for (const attributeValueDto of attributeDto.attributeValues) {
      //     const attributeValue = this.productAttributeValueRepository.create({
      //       ...attributeValueDto,
      //       productAttribute: attribute,
      //     });
      //     attributeValues.push(attributeValue);
      //   }
      //   await queryRunner.manager.save(attributeValues);
      // }

      await queryRunner.commitTransaction();

      return product;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException();
    } finally {
      await queryRunner.release();
    }
  }

  findAll(filter: { categoryId: number; branchId: number }, user: any) {
    let where: FindOptionsWhere<Product> = {};

    if (filter.categoryId) {
      where = {
        ...where,
        category: {
          id: filter.categoryId,
        },
      };
    }

    if (filter.branchId) {
      where = {
        ...where,
        branches: {
          id: filter.branchId,
        },
      };
    }

    return this.productRepository1.repository.find({
      where: {
        ...where,
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
      relations: {
        category: true,
        unit: true,
        branches: true,
        vendors: true,
      },
      order: {
        id: 'ASC',
      },
    });
  }

  async findOne(id: number) {
    const product = await this.productRepository1.repository.findOne({
      where: { id },
      relations: {
        category: true,
        productAttributes: {
          productAttributeValues: true,
        },
        unit: true,
        branches: true,
        vendors: true,
        store: true,
      },
    });

    if (!product) throw new NotFoundException('product not found');

    return product;
  }

  async update(id: number, updateProductDto: UpdateProductDto, user: any) {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: {
        branches: true,
        productAttributes: {
          productAttributeValues: true,
        },
        store: true,
        vendors: true,
      },
    });

    if (!product) {
      throw new NotFoundException('product not found');
    }

    //check code exist
    const codeExistQuery = this.productRepository.exists({
      where: {
        code: updateProductDto?.code,
        id: Not(id),
        ...user?.storeId && ({ store: { id: user.storeId } })
      },
    });

    //check barcode exist
    const barcodeExistQuery = this.productRepository.exists({
      where: {
        barcode: updateProductDto?.barcode,
        id: Not(id),
        ...user?.storeId && ({ store: { id: user.storeId } })
      },
    });

    const [codeExist, barcodeExist] = await Promise.all([
      codeExistQuery,
      barcodeExistQuery,
    ]);

    if (codeExist) {
      throw new BadRequestException('Code already exists');
    }

    if (barcodeExist) {
      throw new BadRequestException('Barcode already exists');
    }

    //start transaction
    const queryRunner = this.dataSource.createQueryRunner();

    const productRepository = queryRunner.manager.getRepository(Product);

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      const branches = (updateProductDto.branchIds.length)
        ? await Branch.find({ where: { id: In(updateProductDto.branchIds) } })
        : [];

      const vendors = await Vendor.findBy({
        id: In(updateProductDto.vendorId),
      });

      product.name = updateProductDto.name;
      product.code = updateProductDto.code;
      product.price = updateProductDto.price;
      product.cost = updateProductDto.cost;
      product.active = updateProductDto.active || false;
      product.remark = updateProductDto.remark;
      product.barcode = updateProductDto.barcode;
      product.showType = updateProductDto.showType;
      product.color = updateProductDto?.color;
      product.image = updateProductDto?.image;

      product.category = { id: updateProductDto.categoryId } as any;
      product.unit = { id: updateProductDto.unitId } as any;

      product.vendors = vendors;

      await productRepository.save(product);

      await queryRunner.commitTransaction();

      return this.findOne(id);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException();
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const product = await this.findById(id);

    if (!product) throw new NotFoundException('product not found');

    this.productRepository1.repository.softRemove(product);
  }

  findById(id: number) {
    return this.productRepository1.repository.findOne({
      where: {
        id,
      },
      relations: {
        category: true,
        unit: true,
        branches: true,
        store: true,
      },
    });
  }

  datatables(query: PaginateQuery, user: any): Promise<Paginated<Product>> {
    return paginate(
      query,
      this.productRepository1.repository,
      {
        ...PRODUCT_PAGINATION_CONFIG,
        where: {
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
    );
  }

  createAttribute(productId: number, dto: CreateProductAttributeDto) {
    return this.productRepository1.createAttribute(productId, dto);
  }

  async deleteAttribute(productAttributeId: number) {
    // Check if the ProductAttribute exists
    const isExist =
      await this.productRepository1.findAttribute(productAttributeId);
    if (!isExist) {
      throw new NotFoundException('Product attribute not found');
    }

    // Find ProductAttributeValue entities related to the ProductAttribute
    const attributeValues = await this.productAttributeValueRepository.find({
      where: { productAttribute: { id: productAttributeId } },
    });

    // Soft delete all related ProductAttributeValue entities
    if (attributeValues.length > 0) {
      await this.productAttributeValueRepository.softRemove(attributeValues);
    }

    // Delete the ProductAttribute
    await this.productRepository1.deleteAttribute(productAttributeId);
  }

  async updateAttribute(
    queryRunner: QueryRunner,
    productId: number,
    oldData: ProductAttribute[],
    newData: CreateProductAttributeDto[],
  ) {
    const productAttributeRepository =
      queryRunner.manager.getRepository(ProductAttribute);
    const productAttributeValueRepository = queryRunner.manager.getRepository(
      ProductAttributeValue,
    );

    const oldDataIds = oldData.map((e) => e.id);
    const newDataIds = [];

    for (const data of newData) {
      if (data.id == null) {
        const result = await productAttributeRepository.save({
          product: {
            id: productId,
          },
          name: data.name,
          type: data.type,
        });

        await this.createAttributeValue(
          queryRunner,
          result.id,
          data.attributeValues,
        );

        newDataIds.push(result.id);
      } else {
        await productAttributeRepository.update(data.id, {
          name: data.name,
          type: data.type,
        });

        await this.updateAttributeValue(
          queryRunner,
          data.id,
          data.attributeValues,
        );

        newDataIds.push(data.id);
      }
    }

    //delete row
    const deleteIds = oldDataIds.filter((e) => !newDataIds.includes(e));
    for (const id of deleteIds) {
      // Fetch related ProductAttributeValue entities
      const attributeValues = await productAttributeValueRepository.find({
        where: { productAttribute: { id } },
      });

      // Soft-remove the related ProductAttributeValue entities
      if (attributeValues.length > 0) {
        await productAttributeValueRepository.softRemove(attributeValues);
      }

      // Soft-remove the ProductAttribute entity
      await productAttributeRepository.softRemove({ id });
    }
  }

  async createAttributeValue(
    queryRunner: QueryRunner,
    attributeId: number,
    newData: CreateProductAttributeValueDto[],
  ) {
    const attributeValueRepository = queryRunner.manager.getRepository(
      ProductAttributeValue,
    );

    const attributeValues = [];
    for (const data of newData) {
      const attributeValue = attributeValueRepository.create({
        productAttribute: {
          id: attributeId,
        },
        name: data.name,
        price: data.price,
      });
      attributeValues.push(attributeValue);
    }
    await attributeValueRepository.save(attributeValues);
  }

  async updateAttributeValue(
    queryRunner: QueryRunner,
    attributeId: number,
    newData: CreateProductAttributeValueDto[],
  ) {
    const attributeValueRepository = queryRunner.manager.getRepository(
      ProductAttributeValue,
    );

    const attributeValues = await attributeValueRepository.find({
      where: { productAttribute: { id: attributeId } },
    });
    const currentAttValueIds = attributeValues.map((e) => e.id);
    const newAttValueIds = [];

    for (const data of newData) {
      if (data.id == null) {
        //new
        const result = await attributeValueRepository.save({
          productAttribute: {
            id: attributeId,
          },
          name: data.name,
          price: data.price,
        });
        newAttValueIds.push(result.id);
      } else {
        //update
        await attributeValueRepository.update(data.id, {
          name: data.name,
          price: data.price,
        });
        newAttValueIds.push(data.id);
      }
    }

    //delete row
    const deleteIds = currentAttValueIds.filter(
      (e) => !newAttValueIds.includes(e),
    );
    await attributeValueRepository.softRemove(
      deleteIds.map((e) => new ProductAttributeValue({ id: e })),
    );
  }

  async import(file: Express.Multer.File) {
    const HEADERS = [
      'Product Code',
      'Product Name',
      'Price',
      'Cost',
      'Category Code',
      'Unit',
      'Branch Code',
      'Barcode',
      'Remark',
      'Attribute Type',
      'Attribute Name',
      'Attribute Value',
      'Attribute Price',
    ];

    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1 });
    const actualHeaders: string[] = jsonData[0] as Array<string>;

    const isValid = HEADERS.every((header) => actualHeaders.includes(header));
    if (!isValid) {
      throw new BadRequestException('Header validation failed.');
    }

    const result = {
      create: 0,
      update: 0,
      ok: 0,
      error: 0,
      errorDetails: [],
    };

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let currentProduct = null;
    const productOps = [];
    const attributeOps = [];
    const attributeValueOps = [];

    const productCache = new Map<string, any>();
    const attributeCache = new Map<string, Map<string, any>>();

    try {
      const branchCache = new Map<string, any>();
      const categoryCache = new Map<string, any>();
      const unitCache = new Map<string, any>();

      for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
        const row = jsonData[rowIndex];
        try {
          const productCode = row[actualHeaders.indexOf('Product Code')];
          if (productCode) {
            const mandatoryFields = [
              'Product Code',
              'Product Name',
              'Price',
              'Cost',
              'Unit',
            ];
            for (const field of mandatoryFields) {
              if (!row[actualHeaders.indexOf(field)]) {
                throw new Error(`${field} is required`);
              }
            }

            const branchCodeString =
              row[actualHeaders.indexOf('Branch Code')].toString();
            const branchCodeArray = branchCodeString
              .split(',')
              .map((code) => code.trim());
            let branches = branchCodeArray.map((code) => branchCache.get(code));
            if (branches.includes(undefined)) {
              branches = await this.branchRepository.find({
                where: { code: In(branchCodeArray) },
              });
              if (branches.length !== branchCodeArray.length)
                throw new Error(`Some branches not found`);
              branches.forEach((branch) =>
                branchCache.set(branch.code, branch),
              );
            }

            const categoryCode = row[actualHeaders.indexOf('Category Code')];
            let category = categoryCache.get(categoryCode);
            if (!category) {
              category = await this.categoryRepository.findOne({
                where: { code: categoryCode },
              });
              if (!category) throw new Error(`Category not found`);
              categoryCache.set(categoryCode, category);
            }

            const unitName = row[actualHeaders.indexOf('Unit')];
            let unit = unitCache.get(unitName);
            if (!unit) {
              unit = await this.unitRepository.findOne({
                where: { name: unitName },
              });
              if (!unit) throw new Error(`Unit not found`);
              unitCache.set(unitName, unit);
            }

            if (!productCache.has(productCode)) {
              let product = await this.productRepository.findOne({
                where: { code: productCode },
                relations: {
                  productAttributes: { productAttributeValues: true },
                },
              });

              if (!product) {
                product = this.productRepository.create({
                  code: productCode,
                  name: row[actualHeaders.indexOf('Product Name')],
                  price: row[actualHeaders.indexOf('Price')],
                  cost: row[actualHeaders.indexOf('Cost')],
                  category,
                  unit,
                  branches,
                  barcode: row[actualHeaders.indexOf('Barcode')],
                  remark: row[actualHeaders.indexOf('Remark')],
                  active: true,
                  // remarkStatus: true,
                  // vatStatus: true,
                });
                product.productAttributes = [];
                await queryRunner.manager.save(product);
                result.create += 1;
              } else {
                product.name = row[actualHeaders.indexOf('Product Name')];
                product.price = row[actualHeaders.indexOf('Price')];
                product.cost = row[actualHeaders.indexOf('Cost')];
                product.category = category;
                product.unit = unit;
                product.branches = branches;
                product.barcode = row[actualHeaders.indexOf('Barcode')];
                product.remark = row[actualHeaders.indexOf('Remark')];
                await queryRunner.manager.save(product);
                result.update += 1;
              }

              productCache.set(productCode, product);
              currentProduct = product;
            } else {
              currentProduct = productCache.get(productCode);
            }
          }

          if (currentProduct && row[actualHeaders.indexOf('Attribute Name')]) {
            const attributeName = row[actualHeaders.indexOf('Attribute Name')];
            const attributeType = row[actualHeaders.indexOf('Attribute Type')];
            const attributeKey = `${attributeName}_${attributeType}`;

            if (!attributeCache.has(currentProduct.code)) {
              attributeCache.set(currentProduct.code, new Map());
            }
            const productAttributesMap = attributeCache.get(
              currentProduct.code,
            );

            let attribute = productAttributesMap.get(attributeKey);
            if (!attribute) {
              if (!currentProduct.productAttributes) {
                currentProduct.productAttributes = [];
              }
              attribute = currentProduct.productAttributes.find(
                (attr) =>
                  attr.name === attributeName && attr.type === attributeType,
              );
              if (!attribute) {
                attribute = this.productAttributeRepository.create({
                  name: attributeName,
                  type: attributeType || 'single',
                  product: currentProduct,
                });
                await queryRunner.manager.save(attribute);
                currentProduct.productAttributes.push(attribute);
              }
              productAttributesMap.set(attributeKey, attribute);
            }

            const attributeValue =
              row[actualHeaders.indexOf('Attribute Value')];
            const attributePrice = parseFloat(
              row[actualHeaders.indexOf('Attribute Price')] || '0',
            );
            let attributeValueEntity = attribute.productAttributeValues
              ? attribute.productAttributeValues.find(
                (val) => val.name === attributeValue,
              )
              : null;

            if (!attribute.productAttributeValues) {
              attribute.productAttributeValues = [];
            }

            if (!attributeValueEntity) {
              attributeValueEntity =
                this.productAttributeValueRepository.create({
                  name: attributeValue,
                  price: attributePrice,
                  productAttribute: attribute,
                });
              await queryRunner.manager.save(attributeValueEntity);
              attribute.productAttributeValues.push(attributeValueEntity);
            }
          }

          result.ok += 1;
        } catch (error) {
          console.error(error);
          result.error += 1;
          result.errorDetails.push({
            row: rowIndex + 1,
            error: `on row ${rowIndex + 1}, ${error.message}`,
          });
        }
      }

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
      return result;
    }
  }

  async findCategoryIdByCode(code: string): Promise<number> {
    const category = await this.categoryRepository.findOne({ where: { code } });
    if (!category) {
      throw new NotFoundException(`Category with code ${code} not found`);
    }
    return category.id;
  }

  async findUnitIdByName(name: string): Promise<number> {
    const unit = await this.unitRepository.findOne({ where: { name } });
    if (!unit) {
      throw new NotFoundException(`Unit with name ${name} not found`);
    }
    return unit.id;
  }

  async findBranchIdByCode(code: string): Promise<number> {
    const branch = await this.branchRepository.findOne({ where: { code } });
    if (!branch) {
      throw new NotFoundException(`Branch with code ${code} not found`);
    }
    return branch.id;
  }

  async findByBarcode(barcode: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { barcode },
      relations: {
        category: true,
        productAttributes: {
          productAttributeValues: true,
        },
        unit: true,
        branches: true,
      },
    });

    if (!product) throw new NotFoundException('Product not found');

    return product;
  }

  async exportProduct() {
    const workbook = new Workbook();

    // Create the original sheet 'product'
    const worksheetProduct = workbook.addWorksheet('product');

    // Add headers for the 'product' sheet
    worksheetProduct.insertRow(1, [
      'Product Code',
      'Product Name',
      'Price',
      'Cost',
      'Category Code',
      'Unit',
      'Branch Code',
      'Barcode',
      'Remark',
      'Attribute Type',
      'Attribute Name',
      'Attribute Value',
      'Attribute Price',
    ]);

    // Set column widths for 'product' sheet
    worksheetProduct.columns = [
      { width: 12 },
      { width: 20 },
      { width: 8 },
      { width: 8 },
      { width: 18 },
      { width: 8 },
      { width: 16 },
      { width: 12 },
      { width: 10 },
      { width: 12 }, // Attribute Type
      { width: 20 }, // Attribute Name
      { width: 20 }, // Attribute Value
      { width: 10 }, // Attribute Price
    ];

    // Fetch products with all related entities
    const dataproduct = await Product.find({
      relations: {
        category: true,
        unit: true,
        branches: true,
        productAttributes: {
          productAttributeValues: true,
        },
      },
    });

    // Loop through each product and its attributes for 'product' sheet
    dataproduct.forEach((product) => {
      let firstRow = true;
      product.productAttributes.forEach((attribute) => {
        attribute.productAttributeValues.forEach((attributeValue) => {
          const row = [
            firstRow ? product.code : '', // Only show product code on the first row for each product
            firstRow ? product.name : '', // Only show product name on the first row for each product
            firstRow ? product.price : '', // Only show price on the first row
            firstRow ? product.cost : '', // Only show cost on the first row
            firstRow ? product.category.code : '', // Only show category code on the first row
            firstRow ? product.unit.name : '', // Only show unit on the first row
            firstRow
              ? product.branches.map((branch) => branch.code).join(',')
              : '', // Only show branch code on the first row
            firstRow ? product.barcode : '', // Only show barcode on the first row
            firstRow ? product.remark : '', // Only show remark on the first row
            attribute.type, // Attribute Type
            attribute.name, // Attribute Name
            attributeValue.name, // Attribute Value
            attributeValue.price, // Attribute Price
          ];
          worksheetProduct.addRow(row);
          firstRow = false; // Ensure subsequent rows for this product don't repeat the product details
        });
      });

      // If the product has no attributes, add a single row with just the product information
      if (product.productAttributes.length === 0) {
        const row = [
          product.code,
          product.name,
          product.price,
          product.cost,
          product.category.code,
          product.unit.name,
          product.branches.map((branch) => branch.code).join(','),
          product.barcode,
          product.remark,
          '',
          '',
          '',
          '', // Empty columns for attributes
        ];
        worksheetProduct.addRow(row);
      }
    });

    // Add mockup data to the 'Template' sheet
    const worksheetTemplate = workbook.addWorksheet('Template');
    worksheetTemplate.insertRow(1, [
      'Product Code',
      'Product Name',
      'Price',
      'Cost',
      'Category Code',
      'Unit',
      'Branch Code',
      'Barcode',
      'Remark',
      'Attribute Type',
      'Attribute Name',
      'Attribute Value',
      'Attribute Price',
    ]);

    // Set column widths for 'Template' sheet
    worksheetTemplate.columns = [
      { width: 15 }, // Product Code
      { width: 20 }, // Product Name
      { width: 10 }, // Price
      { width: 10 }, // Cost
      { width: 18 }, // Category Code
      { width: 12 }, // Unit
      { width: 15 }, // Branch Code
      { width: 15 }, // Barcode
      { width: 20 }, // Remark
      { width: 15 }, // Attribute Type
      { width: 20 }, // Attribute Name
      { width: 20 }, // Attribute Value
      { width: 12 }, // Attribute Price
    ];

    // Add mockup data (similar to your image)
    const mockupData = [
      [
        'Example1',
        'Example 1',
        0,
        0,
        'category code',
        'unit name',
        'BranchCode',
        'barcode',
        'Example',
        'single',
        'Example',
        'Example',
        0,
      ],
      ['', '', 0, 0, '', '', '', '', '', 'single', 'Example', 'Example', 0],
      ['', '', 0, 0, '', '', '', '', '', 'multiple', 'Example', 'Example', 0],
      [
        'Example2',
        'Example 2',
        0,
        0,
        'category code',
        'unit name',
        'BranchCode',
        'barcode',
        'Example',
        'single',
        'Example',
        'Example',
        0,
      ],
      ['', '', 0, 0, '', '', '', '', '', 'single', 'Example', 'Example', 0],
      ['', '', 0, 0, '', '', '', '', '', 'multiple', 'Example', 'Example', 0],
    ];

    mockupData.forEach((row) => {
      worksheetTemplate.addRow(row);
    });

    // Return the buffer for the Excel file
    return workbook.xlsx.writeBuffer();
  }

  async findByCode(code: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { code: ILike(code) },
      relations: {
        category: true,
        productAttributes: {
          productAttributeValues: true,
        },
        unit: true,
        branches: true,
      },
    });

    if (!product) throw new NotFoundException('Product not found');

    return product;
  }

  async findAllforOrder(): Promise<Product[]> {
    return this.productRepository.find(); // Adjust according to your needs
  }
}
