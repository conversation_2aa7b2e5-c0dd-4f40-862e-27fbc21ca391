import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { StockAdjustment } from './stock-adjustment.entity';
import { Product } from '../../product/entities/product.entity';
import { Inventory } from '../../inventory/entities/inventory.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

export enum AdjustmentItemType {
  INCREASE = 'increase', // เพิ่มสต็อก
  DECREASE = 'decrease', // ลดสต็อก
  SET = 'set' // กำหนดสต็อกใหม่
}

@Entity()
export class StockAdjustmentItem extends CustomBaseEntity {
  @Column({
    type: 'enum',
    enum: AdjustmentItemType,
    comment: 'ประเภทการปรับสต็อกของรายการนี้'
  })
  adjustmentType: AdjustmentItemType;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกก่อนปรับ'
  })
  stockBefore: number;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกหลังปรับ'
  })
  stockAfter: number;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนที่ปรับ (บวก = เพิ่ม, ลบ = ลด)'
  })
  adjustmentQuantity: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนต่อหน่วยในขณะที่ปรับ'
  })
  unitCost: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่าการปรับ (จำนวนที่ปรับ × ต้นทุนต่อหน่วย)'
  })
  adjustmentValue: number;

  @Column({
    nullable: true,
    comment: 'เหตุผลการปรับสำหรับรายการนี้'
  })
  reason: string;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมสำหรับรายการนี้'
  })
  notes: string;

  @Column({
    nullable: true,
    comment: 'หมายเลขแบทช์หรือล็อต'
  })
  batchNo: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'วันหมดอายุสำหรับสินค้าที่เสื่อมเสีย'
  })
  expiryDate: Date;

  @Column({
    nullable: true,
    comment: 'ตำแหน่งที่เก็บสินค้า'
  })
  location: string;

  // ความสัมพันธ์
  @ManyToOne(() => StockAdjustment, (stockAdjustment) => stockAdjustment.items)
  @JoinColumn({ name: 'stock_adjustment_id' })
  stockAdjustment: StockAdjustment;

  @ManyToOne(() => Product, (product) => product.stockAdjustmentItems)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Inventory, (inventory) => inventory.stockAdjustmentItems)
  @JoinColumn({ name: 'inventory_id' })
  inventory: Inventory;

  constructor(partial?: Partial<StockAdjustmentItem>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
