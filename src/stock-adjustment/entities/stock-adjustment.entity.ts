import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { User } from '../../user/entities/user.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Store } from '../../store/entities/store.entity';
import { StockAdjustmentItem } from './stock-adjustment-item.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

export enum StockAdjustmentStatus {
  DRAFT = 'draft', // ร่าง
  PENDING = 'pending', // รอดำเนินการ
  APPROVED = 'approved', // อนุมัติแล้ว
  COMPLETED = 'completed', // เสร็จสิ้น
  CANCELLED = 'cancelled' // ยกเลิก
}

export enum StockAdjustmentType {
  INCREASE = 'increase', // เพิ่มสต็อก
  DECREASE = 'decrease', // ลดสต็อก
  RECOUNT = 'recount', // นับสต็อกใหม่
  DAMAGE = 'damage', // สินค้าเสียหาย
  EXPIRED = 'expired', // สินค้าหมดอายุ
  LOST = 'lost', // สินค้าสูญหาย
  FOUND = 'found', // พบสินค้าเพิ่ม
  OTHER = 'other' // อื่นๆ
}

@Entity()
export class StockAdjustment extends CustomBaseEntity {
  @Column({
    comment: 'หมายเลขใบปรับสต็อกที่ไม่ซ้ำ'
  })
  @Index({ unique: true })
  adjustmentNo: string;

  @Column({
    type: 'enum',
    enum: StockAdjustmentType,
    comment: 'ประเภทของการปรับสต็อก'
  })
  type: StockAdjustmentType;

  @Column({
    type: 'enum',
    enum: StockAdjustmentStatus,
    default: StockAdjustmentStatus.DRAFT,
    comment: 'สถานะของใบปรับสต็อก'
  })
  status: StockAdjustmentStatus;

  @Column({
    type: 'date',
    comment: 'วันที่ของใบปรับสต็อก'
  })
  adjustmentDate: Date;

  @Column({
    nullable: true,
    comment: 'หมายเลขอ้างอิงเอกสารภายนอก'
  })
  referenceNo: string;

  @Column({
    nullable: true,
    comment: 'เหตุผลในการปรับสต็อก'
  })
  reason: string;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนรายการสินค้าทั้งหมด'
  })
  totalItems: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าที่ปรับเพิ่ม'
  })
  totalIncreaseQuantity: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าที่ปรับลด'
  })
  totalDecreaseQuantity: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่าการปรับเพิ่ม'
  })
  totalIncreaseValue: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่าการปรับลด'
  })
  totalDecreaseValue: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่าสุทธิของการปรับ (เพิ่ม - ลด)'
  })
  netAdjustmentValue: number;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมเกี่ยวกับใบปรับสต็อก'
  })
  notes: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'วันที่และเวลาที่อนุมัติ'
  })
  approvedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'วันที่และเวลาที่เสร็จสิ้น'
  })
  completedAt: Date;

  // ความสัมพันธ์
  @ManyToOne(() => Branch, (branch) => branch.stockAdjustments)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @ManyToOne(() => Store, (store) => store.stockAdjustments)
  @JoinColumn({ name: 'store_id' })
  store: Store;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approvedBy: User;

  @OneToMany(() => StockAdjustmentItem, (adjustmentItem) => adjustmentItem.stockAdjustment, { cascade: true })
  items: StockAdjustmentItem[];

  constructor(partial?: Partial<StockAdjustment>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
