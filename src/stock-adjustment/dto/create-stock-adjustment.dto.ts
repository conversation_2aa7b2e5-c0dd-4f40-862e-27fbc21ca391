import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum, IsDateString, IsArray, ValidateNested, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { StockAdjustmentType, StockAdjustmentStatus } from '../entities/stock-adjustment.entity';
import { AdjustmentItemType } from '../entities/stock-adjustment-item.entity';

export class CreateStockAdjustmentItemDto {
  @ApiProperty({ description: 'รหัสสินค้า' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้า' })
  @IsNumber({}, { message: 'รหัสสินค้าต้องเป็นตัวเลข' })
  readonly productId: number;

  @ApiProperty({ description: 'รหัสสินค้าคงคลัง' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้าคงคลัง' })
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังต้องเป็นตัวเลข' })
  readonly inventoryId: number;

  @ApiProperty({
    description: 'ประเภทการปรับสต็อก',
    enum: AdjustmentItemType
  })
  @IsNotEmpty({ message: 'กรุณาระบุประเภทการปรับ' })
  @IsEnum(AdjustmentItemType, { message: 'ประเภทการปรับไม่ถูกต้อง' })
  readonly adjustmentType: AdjustmentItemType;

  @ApiProperty({ description: 'จำนวนสต็อกหลังปรับ (สำหรับ SET) หรือจำนวนที่ปรับ (สำหรับ INCREASE/DECREASE)' })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวน' })
  @IsNumber({}, { message: 'จำนวนต้องเป็นตัวเลข' })
  readonly quantity: number;

  @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ต้นทุนต่อหน่วยต้องเป็นตัวเลข' })
  @Min(0, { message: 'ต้นทุนต่อหน่วยต้องไม่ติดลบ' })
  readonly unitCost?: number;

  @ApiProperty({ description: 'เหตุผลการปรับสำหรับรายการนี้', required: false })
  @IsOptional()
  @IsString({ message: 'เหตุผลต้องเป็นข้อความ' })
  readonly reason?: string;

  @ApiProperty({ description: 'หมายเหตุสำหรับรายการนี้', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ description: 'หมายเลขแบทช์หรือล็อต', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขแบทช์ต้องเป็นข้อความ' })
  readonly batchNo?: string;

  @ApiProperty({ description: 'วันหมดอายุ', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'วันหมดอายุต้องเป็นรูปแบบวันที่ที่ถูกต้อง' })
  readonly expiryDate?: string;

  @ApiProperty({ description: 'ตำแหน่งที่เก็บ', required: false })
  @IsOptional()
  @IsString({ message: 'ตำแหน่งที่เก็บต้องเป็นข้อความ' })
  readonly location?: string;
}

export class CreateStockAdjustmentDto {
  @ApiProperty({ description: 'รหัสสาขา' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสาขา' })
  @IsNumber({}, { message: 'รหัสสาขาต้องเป็นตัวเลข' })
  readonly branchId: number;

  @ApiProperty({
    description: 'ประเภทของการปรับสต็อก',
    enum: StockAdjustmentType
  })
  @IsNotEmpty({ message: 'กรุณาระบุประเภทการปรับสต็อก' })
  @IsEnum(StockAdjustmentType, { message: 'ประเภทการปรับสต็อกไม่ถูกต้อง' })
  readonly type: StockAdjustmentType;

  @ApiProperty({
    description: 'สถานะของใบปรับสต็อก',
    enum: StockAdjustmentStatus,
    default: StockAdjustmentStatus.DRAFT,
    required: false
  })
  @IsOptional()
  @IsEnum(StockAdjustmentStatus, { message: 'สถานะใบปรับสต็อกไม่ถูกต้อง' })
  readonly status?: StockAdjustmentStatus = StockAdjustmentStatus.DRAFT;

  @ApiProperty({ description: 'วันที่ของใบปรับสต็อก' })
  @IsNotEmpty({ message: 'กรุณาระบุวันที่' })
  @IsDateString({}, { message: 'วันที่ต้องเป็นรูปแบบวันที่ที่ถูกต้อง' })
  readonly adjustmentDate: string;

  @ApiProperty({ description: 'หมายเลขอ้างอิงเอกสารภายนอก', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขอ้างอิงต้องเป็นข้อความ' })
  readonly referenceNo?: string;

  @ApiProperty({ description: 'เหตุผลในการปรับสต็อก', required: false })
  @IsOptional()
  @IsString({ message: 'เหตุผลต้องเป็นข้อความ' })
  readonly reason?: string;

  @ApiProperty({ description: 'หมายเหตุเพิ่มเติม', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ 
    description: 'รายการสินค้าที่ต้องการปรับ',
    type: [CreateStockAdjustmentItemDto]
  })
  @IsNotEmpty({ message: 'กรุณาระบุรายการสินค้า' })
  @IsArray({ message: 'รายการสินค้าต้องเป็น array' })
  @ValidateNested({ each: true })
  @Type(() => CreateStockAdjustmentItemDto)
  readonly items: CreateStockAdjustmentItemDto[];
}
