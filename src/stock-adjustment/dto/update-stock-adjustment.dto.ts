import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { CreateStockAdjustmentDto } from './create-stock-adjustment.dto';
import { StockAdjustmentStatus } from '../entities/stock-adjustment.entity';

export class UpdateStockAdjustmentDto extends PartialType(CreateStockAdjustmentDto) {
  @ApiProperty({
    description: 'สถานะของใบปรับสต็อก',
    enum: StockAdjustmentStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(StockAdjustmentStatus, { message: 'สถานะใบปรับสต็อกไม่ถูกต้อง' })
  readonly status?: StockAdjustmentStatus;
}

export class ApproveStockAdjustmentDto {
  @ApiProperty({ description: 'หมายเหตุการอนุมัติ', required: false })
  @IsOptional()
  readonly approvalNotes?: string;
}

export class CompleteStockAdjustmentDto {
  @ApiProperty({ description: 'หมายเหตุการเสร็จสิ้น', required: false })
  @IsOptional()
  readonly completionNotes?: string;
}
