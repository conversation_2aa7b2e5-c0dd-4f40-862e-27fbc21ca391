import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class ReportTapcardDto {
  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;

  @ApiProperty({ required: false, default: null })
  readonly branchID: number | null = null;

  @ApiProperty({ required: false, default: null })
  readonly categoryID: number | null = null;

  @ApiProperty({ required: false, default: null })
  readonly productID: number | null = null;

  @ApiProperty({ required: false, default: null })
  Type: string;
}
