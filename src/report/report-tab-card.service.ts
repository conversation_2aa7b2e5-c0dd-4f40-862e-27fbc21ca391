import { Injectable } from '@nestjs/common';
import { Fill, Workbook } from 'exceljs';
import { CreateReportDto } from './dto/create-report.dto';
import { UpdateReportDto } from './dto/update-report.dto';
import { OrderService } from 'src/order/order.service';
import { DateTime } from 'luxon';
import { Branch } from 'src/branch/entities/branch.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { Shift } from 'src/shift/entities/shift.entity';
import { Order } from 'src/order/entities/order.entity';
import * as ExcelJS from 'exceljs';

@Injectable()
export class ReportTabCardService {
  constructor(
    private orderService: OrderService,
    @InjectRepository(Branch) private branchRepository: Repository<Branch>,
    @InjectRepository(Shift) private shiftRepository: Repository<Shift>,
    @InjectRepository(Order) private orderRepository: Repository<Order>,
    // @InjectRepository(TapLog) private taplogRepository: Repository<TapLog>,
  ) {}

  create(createReportDto: CreateReportDto) {
    return 'This action adds a new report';
  }

  // async findAll() {
  //   const now = DateTime.now();

  //   const start: Date = DateTime.now()
  //     .startOf('day')
  //     .plus({ hours: 10, minutes: 30 })
  //     .toJSDate();
  //   const end: Date = DateTime.now()
  //     .startOf('day')
  //     .plus({ hours: 14 })
  //     .toJSDate();

  //   // if (now.hour < 5) {
  //   //   start = DateTime.now().startOf('day').minus({ day: 1 }).plus({ hour: 5 }).toJSDate();
  //   //   end = DateTime.now().endOf('day').minus({ day: 1 }).plus({ hour: 5 }).toJSDate();
  //   // } else {
  //   //   start = DateTime.now().startOf('day').plus({ hour: 5 }).toJSDate();
  //   //   end = DateTime.now().endOf('day').plus({ hour: 5 }).toJSDate();
  //   // }
  //   const data = await this.taplogRepository.find({
  //     where: {
  //       datetime: Between(start, end),
  //     },
  //     relations: ['card'],
  //   });
  //   return data;
  // }

  // async reportSummaryTapLog(startDate: Date, endDate: Date) {
  //   const workbook = new Workbook();

  //   const worksheet = workbook.addWorksheet('Data', {});

  //   const HEADER_FONT_STYLE = { bold: true };
  //   const HEADER_FILL_STYLE: Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'FFFF00' },
  //     fgColor: { argb: 'FFFF00' },
  //   };

  //   const SUBTOTAL_FONT_STYLE = { bold: true };
  //   const SUBTOTAL_FILL_STYLE: ExcelJS.Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'FFFF00' },
  //     fgColor: { argb: 'FFFF00' },
  //   };

  //   worksheet.columns = [
  //     { header: 'Date', key: 'date', width: 15 },
  //     { header: 'Description', key: 'description', width: 30 },
  //     { header: 'Total Tab count', key: 'tabCount', width: 20 },
  //     { header: 'Amount', key: 'amount', width: 15 },
  //     { header: 'VAT 7%', key: 'vat', width: 10 },
  //     { header: 'Total Amount', key: 'totalAmount', width: 20 },
  //   ];
  //   const header = worksheet.getRow(1);
  //   header.eachCell((c, n) => {
  //     c.fill = HEADER_FILL_STYLE;
  //     c.font = HEADER_FONT_STYLE;
  //     c.border = {
  //       top: { style: 'thin' },
  //       left: { style: 'thin' },
  //       bottom: { style: 'thin' },
  //       right: { style: 'thin' },
  //     };
  //   });

  //   const content = await this.taplogRepository.find({
  //     relations: {
  //       member: true,
  //       shift: true,
  //     },
  //     where: {
  //       datetime: Between(startDate, endDate),
  //     },
  //   });

  //   const startDate1 = '10:30:00';
  //   const endDate1 = '14:00:00';
  //   const startDate2 = '23:00:00';
  //   const endDate2 = '02:00:00';

  //   const groupedData = content.reduce((acc, log) => {
  //     const logDate = new Date(log.datetime);
  //     const dateKey = logDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  //     if (!acc[dateKey]) {
  //       acc[dateKey] = {
  //         'subsidy lunch shift 1': 0,
  //         'subsidy lunch shift 2': 0,
  //       };
  //     }
  //     const time = logDate.toTimeString().split(' ')[0]; // Format as HH:MM:SS
  //     // Shift 1: Between startDate1 and endDate1
  //     if (time >= startDate1 && time <= endDate1) {
  //       acc[dateKey]['subsidy lunch shift 1'] += 1;
  //     }
  //     // Shift 2: Between startDate2 and endDate2, taking into account the midnight crossing
  //     else {
  //       const logHour = logDate.getHours();
  //       const logMinute = logDate.getMinutes();
  //       const startHour2 = parseInt(startDate2.split(':')[0], 10);
  //       const endHour2 = parseInt(endDate2.split(':')[0], 10);
  //       const endMinute2 = parseInt(endDate2.split(':')[1], 10);
  //       // Check if log time is between startDate2 and midnight
  //       if (
  //         logHour > startHour2 ||
  //         (logHour === startHour2 &&
  //           logMinute >= parseInt(startDate2.split(':')[1], 10))
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //       // Check if log time is between midnight and endDate2
  //       else if (
  //         logHour < endHour2 ||
  //         (logHour === endHour2 && logMinute <= endMinute2)
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //     }
  //     return acc;
  //   }, {});
  //   // Convert the grouped data object to the desired array format with "date" key
  //   const rawGroupedData = Object.keys(groupedData).map((date) => ({
  //     date: date,
  //     shift1: {
  //       key: 'subsidy lunch shift 1',
  //       value: groupedData[date]['subsidy lunch shift 1'],
  //     },
  //     shift2: {
  //       key: 'subsidy lunch shift 2',
  //       value: groupedData[date]['subsidy lunch shift 2'],
  //     },
  //   }));

  //   const transformedData = rawGroupedData.map((item) => ({
  //     date: item.date,
  //     shift: [
  //       { key: item.shift1.key, value: item.shift1.value },
  //       { key: item.shift2.key, value: item.shift2.value },
  //     ],
  //   }));

  //   const formattedData = rawGroupedData.flatMap((item) => [
  //     {
  //       date: item.date,
  //       description: item.shift1.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift1.value,
  //       amount: item.shift1.value * 76,
  //       vat: item.shift1.value * 76 * 0.07,
  //       totalAmount: item.shift1.value * 76 + item.shift1.value * 76 * 0.07,
  //     },
  //     {
  //       date: item.date,
  //       description: item.shift2.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift2.value,
  //       amount: item.shift2.value * 76,
  //       vat: item.shift2.value * 76 * 0.07,
  //       totalAmount: item.shift2.value * 76 + item.shift2.value * 76 * 0.07,
  //     },
  //   ]);
  //   let lastDate = ''; // ตัวแปรช่วยเพื่อเก็บวันที่ล่าสุดที่ได้เพิ่มเข้าไป
  //   const data = formattedData;
  //   // Add rows using the data provided
  //   data.forEach((item) => {
  //     const row = worksheet.addRow({
  //       date: item.date !== lastDate ? item.date : '',
  //       description: item.description,
  //       tabCount: item.tabCount,
  //       amount: item.amount,
  //       vat: item.vat,
  //       totalAmount: item.totalAmount,
  //     });

  //     // อัปเดต lastDate เป็นวันที่ปัจจุบันถ้าไม่ซ้ำ
  //     if (item.date !== lastDate) {
  //       lastDate = item.date;
  //     }

  //     // ใส่เส้นให้กับทุกเซลล์ในแถว
  //     row.eachCell((cell) => {
  //       cell.border = {
  //         top: { style: 'thin', color: { argb: '000000' } },
  //         left: { style: 'thin', color: { argb: '000000' } },
  //         bottom: { style: 'thin', color: { argb: '000000' } },
  //         right: { style: 'thin', color: { argb: '000000' } },
  //       };
  //     });
  //   });
  //   const startDateFormatted: string = DateTime.fromJSDate(startDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const endDateFormatted: string = DateTime.fromJSDate(endDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const nowDate = DateTime.now()
  //     .setLocale('th-TH')
  //     .toLocaleString({ ...DateTime.DATE_SHORT, month: '2-digit' });
  //   const combinedDate = `${startDateFormatted} ถึงวันที่ ${endDateFormatted}`;
  //   worksheet.insertRow(1, ['Sodexo Services (Thailand) Ltd.']);
  //   worksheet.mergeCells('A1:F1');
  //   worksheet.getCell('A1').font = { size: 14, bold: true };
  //   worksheet.getCell('A1').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(2, ['รายงานสรุปยอดรวมการใช้สิทธิพนักงาน']);
  //   worksheet.mergeCells('A2:F2');
  //   worksheet.getCell('A2').font = { size: 14, bold: true };
  //   worksheet.getCell('A2').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('A2').fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'D3D3D3' },
  //     fgColor: { argb: 'D3D3D3' },
  //   };

  //   worksheet.insertRow(3, ['']);
  //   worksheet.mergeCells('A3:F3');

  //   worksheet.insertRow(4, ['ชื่อสาขา', 'East-West Seed']);
  //   worksheet.getCell('A4').font = { size: 11, bold: true };
  //   worksheet.getCell('A4').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(5, ['รหัสสาขา', '664TH305001']);
  //   worksheet.getCell('A5').font = { size: 11, bold: true };
  //   worksheet.getCell('A5').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(6, [
  //     'ประจำวันที่',
  //     combinedDate,
  //     '',
  //     '',
  //     'พิมพ์',
  //     nowDate,
  //   ]);
  //   worksheet.getCell('A6').font = { size: 11, bold: true };
  //   worksheet.getCell('A6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('E6').font = { size: 11, bold: true };
  //   worksheet.getCell('E6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(7, ['']);
  //   worksheet.insertRow(8, ['']);

  //   // Add subtotals row manually if needed
  //   const subTotalRow = worksheet.addRow([
  //     '',
  //     'Sub Total',
  //     data.reduce((acc, cur) => acc + cur.tabCount, 0),
  //     data.reduce((acc, cur) => acc + cur.amount, 0),
  //     data.reduce((acc, cur) => acc + cur.vat, 0),
  //     data.reduce((acc, cur) => acc + cur.totalAmount, 0),
  //   ]);

  //   subTotalRow.eachCell((cell, index) => {
  //     cell.fill = SUBTOTAL_FILL_STYLE;
  //     cell.font = SUBTOTAL_FONT_STYLE;
  //     cell.border = {
  //       top: { style: 'thin' },
  //       left: { style: 'thin' },
  //       bottom: { style: 'thin' },
  //       right: { style: 'thin' },
  //     };
  //     cell.border = {
  //       top: { style: 'thin', color: { argb: '000000' } },
  //       left: { style: 'thin', color: { argb: '000000' } },
  //       bottom: { style: 'thin', color: { argb: '000000' } },
  //       right: { style: 'thin', color: { argb: '000000' } },
  //     };
  //   });
  //   // Write to buffer
  //   return workbook.xlsx.writeBuffer();
  // }

  // async reportSummaryTapCard(startDate: Date, endDate: Date) {
  //   const workbook = new Workbook();

  //   // Create two worksheets
  //   const worksheetSummary = workbook.addWorksheet('Summary');
  //   const worksheetDetail = workbook.addWorksheet('Detail');

  //   // Call functions to populate each worksheet
  //   await this.reportSummaryTapCardTypeSummary(
  //     worksheetSummary,
  //     startDate,
  //     endDate,
  //   );
  //   await this.reportSummaryTapCardTypeDetail(
  //     worksheetDetail,
  //     startDate,
  //     endDate,
  //   );

  //   // Write to buffer
  //   return workbook.xlsx.writeBuffer();
  // }
  // async reportSummaryTapCardTypeSummary(
  //   worksheet: ExcelJS.Worksheet,
  //   startDate: Date,
  //   endDate: Date,
  // ) {
  //   const TITLE_FONT_STYLE = { size: 10, bold: true };
  //   const TITLE_ALIGNMENT: Partial<ExcelJS.Alignment> = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   const TITLE_FILL_STYLE: Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'D3D3D3' },
  //     fgColor: { argb: 'D3D3D3' },
  //   };

  //   const RECORD_FONT_STYLE = { size: 10 };
  //   const RECORD_ALIGNMENT: Partial<ExcelJS.Alignment> = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   worksheet.columns = [
  //     { header: '', key: '', width: 10 },
  //     { header: 'Card', key: 'card', width: 15 },
  //     { header: 'Code', key: 'code', width: 15 },
  //     { header: 'Name', key: 'name', width: 30 },
  //     { header: 'Card Type', key: 'cardType', width: 10 },
  //     { header: 'Tap count\n(Total)', key: 'tapCount', width: 10 },
  //   ];
  //   const header = worksheet.getRow(1);
  //   header.eachCell((c, n) => {
  //     if (n > 1) {
  //       // Skip the first cell
  //       c.fill = TITLE_FILL_STYLE;
  //       c.font = TITLE_FONT_STYLE;
  //       c.alignment = TITLE_ALIGNMENT;
  //     }
  //   });

  //   const content = await this.taplogRepository.find({
  //     relations: {
  //       member: true,
  //       shift: true,
  //     },
  //     where: {
  //       datetime: Between(startDate, endDate),
  //     },
  //   });

  //   // console.log(content);

  //   function groupByMemberCodeAndCardSn(data) {
  //     return data.reduce((acc, item) => {
  //       const memberCode = item.card.member.code;
  //       const cardSn = item.card.sn;
  //       const memberName =
  //         item.card.member.firstname + ' ' + item.card.member.lastname;
  //       const cardType = item.card.cardType;

  //       if (!acc[memberCode]) {
  //         acc[memberCode] = {};
  //       }

  //       if (!acc[memberCode][cardSn]) {
  //         acc[memberCode][cardSn] = {
  //           memberName,
  //           cardType,
  //           count: 0,
  //           logs: [],
  //         };
  //       }

  //       acc[memberCode][cardSn].count += 1;
  //       acc[memberCode][cardSn].logs.push(item);

  //       return acc;
  //     }, {});
  //   }

  //   const groupdata = groupByMemberCodeAndCardSn(content);

  //   //   console.log(groupdata);

  //   const transformedData = Object.keys(groupdata).reduce(
  //     (result, memberCode) => {
  //       Object.keys(groupdata[memberCode]).forEach((cardSn) => {
  //         const entry = groupdata[memberCode][cardSn];
  //         const transformedEntry = {
  //           card: cardSn,
  //           code: memberCode,
  //           name: entry.memberName,
  //           cardType: entry.cardType,
  //           tapCount: entry.count,
  //         };
  //         result.push(transformedEntry);
  //       });
  //       return result;
  //     },
  //     [],
  //   );

  //   // console.log(transformedData);

  //   const startDate1 = '10:30:00';
  //   const endDate1 = '14:00:00';
  //   const startDate2 = '23:00:00';
  //   const endDate2 = '02:00:00';

  //   const groupedData = content.reduce((acc, log) => {
  //     const logDate = new Date(log.datetime);
  //     const dateKey = logDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  //     if (!acc[dateKey]) {
  //       acc[dateKey] = {
  //         'subsidy lunch shift 1': 0,
  //         'subsidy lunch shift 2': 0,
  //       };
  //     }
  //     const time = logDate.toTimeString().split(' ')[0]; // Format as HH:MM:SS
  //     // Shift 1: Between startDate1 and endDate1
  //     if (time >= startDate1 && time <= endDate1) {
  //       acc[dateKey]['subsidy lunch shift 1'] += 1;
  //     }
  //     // Shift 2: Between startDate2 and endDate2, taking into account the midnight crossing
  //     else {
  //       const logHour = logDate.getHours();
  //       const logMinute = logDate.getMinutes();
  //       const startHour2 = parseInt(startDate2.split(':')[0], 10);
  //       const endHour2 = parseInt(endDate2.split(':')[0], 10);
  //       const endMinute2 = parseInt(endDate2.split(':')[1], 10);
  //       // Check if log time is between startDate2 and midnight
  //       if (
  //         logHour > startHour2 ||
  //         (logHour === startHour2 &&
  //           logMinute >= parseInt(startDate2.split(':')[1], 10))
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //       // Check if log time is between midnight and endDate2
  //       else if (
  //         logHour < endHour2 ||
  //         (logHour === endHour2 && logMinute <= endMinute2)
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //     }
  //     return acc;
  //   }, {});
  //   // Convert the grouped data object to the desired array format with "date" key
  //   const rawGroupedData = Object.keys(groupedData).map((date) => ({
  //     date: date,
  //     shift1: {
  //       key: 'subsidy lunch shift 1',
  //       value: groupedData[date]['subsidy lunch shift 1'],
  //     },
  //     shift2: {
  //       key: 'subsidy lunch shift 2',
  //       value: groupedData[date]['subsidy lunch shift 2'],
  //     },
  //   }));

  //   // rawGroupedData.forEach(i=>{
  //   //     console.log(i);
  //   //     console.log("-------------------------");

  //   // })

  //   const formattedData = rawGroupedData.flatMap((item) => [
  //     {
  //       date: item.date,
  //       description: item.shift1.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift1.value,
  //       amount: item.shift1.value * 76,
  //       vat: item.shift1.value * 76 * 0.07,
  //       totalAmount: item.shift1.value * 76 + item.shift1.value * 76 * 0.07,
  //     },
  //     {
  //       date: item.date,
  //       description: item.shift2.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift2.value,
  //       amount: item.shift2.value * 76,
  //       vat: item.shift2.value * 76 * 0.07,
  //       totalAmount: item.shift2.value * 76 + item.shift2.value * 76 * 0.07,
  //     },
  //   ]);
  //   let lastDate = ''; // ตัวแปรช่วยเพื่อเก็บวันที่ล่าสุดที่ได้เพิ่มเข้าไป
  //   const data = transformedData;

  //   // data.forEach(i =>{
  //   //     console.log(i.date);
  //   //     console.log("-------------------");

  //   // })
  //   // Add rows using the data provided
  //   data.forEach((item) => {
  //     const row = worksheet.addRow({
  //       card: item.card,
  //       code: item.code,
  //       name: item.name,
  //       cardType: item.cardType,
  //       tapCount: item.tapCount,
  //     });

  //     // อัปเดต lastDate เป็นวันที่ปัจจุบันถ้าไม่ซ้ำ
  //     if (item.date !== lastDate) {
  //       lastDate = item.date;
  //     }
  //     row.eachCell((cell, colNumber) => {
  //       if (colNumber > 1) {
  //         cell.font = RECORD_FONT_STYLE;
  //         cell.alignment = RECORD_ALIGNMENT;
  //       }
  //     });
  //   });
  //   const startDateFormatted: string = DateTime.fromJSDate(startDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const endDateFormatted: string = DateTime.fromJSDate(endDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const nowDate = DateTime.now()
  //     .setLocale('th-TH')
  //     .toLocaleString({ ...DateTime.DATE_SHORT, month: '2-digit' });
  //   const combinedDate = `${startDateFormatted} ถึงวันที่ ${endDateFormatted}`;
  //   worksheet.insertRow(1, ['']);

  //   worksheet.insertRow(2, ['', 'บริษัทโซเด็กซ์โซ่ อมตะ เซอร์วิสเซส จำกัด']);
  //   worksheet.mergeCells('B2:F2');
  //   worksheet.getCell('B2').font = { size: 14, bold: true };
  //   worksheet.getCell('B2').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(3, ['', 'รายงานสรุปยอดรวมการใช้สิทธิพนักงาน']);
  //   worksheet.mergeCells('B3:F3');
  //   worksheet.getCell('B3').font = { size: 14, bold: true };
  //   worksheet.getCell('B3').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('B3').fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'D3D3D3' },
  //     fgColor: { argb: 'D3D3D3' },
  //   };

  //   worksheet.insertRow(4, ['']);
  //   worksheet.mergeCells('B4:F4');

  //   worksheet.insertRow(5, ['', 'ชื่อสาขา', 'Essilor', 'รหัสสาขา', '14']);
  //   worksheet.getCell('B5').font = { size: 11, bold: true };
  //   worksheet.getCell('B5').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D5').font = { size: 11, bold: true };
  //   worksheet.getCell('D5').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(6, ['', 'ชื่อพนักงาน', 'ALL', 'รหัสพนักงาน', 'ALL']);
  //   worksheet.getCell('B6').font = { size: 11, bold: true };
  //   worksheet.getCell('B6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D6').font = { size: 11, bold: true };
  //   worksheet.getCell('D6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(7, [
  //     '',
  //     'ประจำวันที่',
  //     startDateFormatted,
  //     'ถึงวันที่',
  //     endDateFormatted,
  //   ]);
  //   worksheet.getCell('B7').font = { size: 11, bold: true };
  //   worksheet.getCell('B7').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D7').font = { size: 11, bold: true };
  //   worksheet.getCell('D7').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(8, ['', 'ข้อมูลแบบ', 'Summmary', '', 'พิมพ์', nowDate]);
  //   worksheet.getCell('B8').font = { size: 11, bold: true };
  //   worksheet.getCell('B8').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('E8').font = { size: 11, bold: true };
  //   worksheet.getCell('E8').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.insertRow(9, ['']);

  //   // Add subtotals row manually if needed
  //   const subTotalRow = worksheet.addRow([
  //     '',
  //     '',
  //     '',
  //     '',
  //     'Sub Total',
  //     data.reduce((acc, cur) => acc + cur.tapCount, 0),
  //   ]);

  //   subTotalRow.eachCell((cell, index) => {
  //     if (index > 4) {
  //       cell.fill = {
  //         type: 'pattern',
  //         pattern: 'solid',
  //         bgColor: { argb: 'ADD8E6' },
  //         fgColor: { argb: 'ADD8E6' },
  //       };
  //       cell.font = TITLE_FONT_STYLE;
  //       cell.alignment = TITLE_ALIGNMENT;
  //     }
  //   });
  // }
  // async reportSummaryTapCardTypeDetail(
  //   worksheet: ExcelJS.Worksheet,
  //   startDate: Date,
  //   endDate: Date,
  // ) {
  //   const TITLE_FONT_STYLE = { size: 10, bold: true };
  //   const TITLE_ALIGNMENT: Partial<ExcelJS.Alignment> = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   const TITLE_FILL_STYLE: Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'D3D3D3' },
  //     fgColor: { argb: 'D3D3D3' },
  //   };

  //   const RECORD_FONT_STYLE = { size: 10 };
  //   const RECORD_ALIGNMENT: Partial<ExcelJS.Alignment> = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };

  //   const SUBTOTAL_FONT_STYLE = { bold: true };
  //   const SUBTOTAL_FILL_STYLE: ExcelJS.Fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'FFFF00' },
  //     fgColor: { argb: 'FFFF00' },
  //   };
  //   worksheet.columns = [
  //     { header: '', key: '', width: 10 },
  //     { header: 'Card', key: 'card', width: 15 },
  //     { header: 'Code', key: 'code', width: 15 },
  //     { header: 'Name', key: 'name', width: 25 },
  //     { header: 'Card Type', key: 'cardType', width: 10 },
  //     { header: 'Shift', key: 'shift', width: 15 },
  //     { header: 'Date', key: 'date', width: 15 },
  //     { header: 'Tap count\n(Total)', key: 'tapCount', width: 10 },
  //   ];
  //   const header = worksheet.getRow(1);
  //   header.eachCell((c, n) => {
  //     if (n > 1) {
  //       // Skip the first cell
  //       c.fill = TITLE_FILL_STYLE;
  //       c.font = TITLE_FONT_STYLE;
  //       c.alignment = TITLE_ALIGNMENT;
  //     }
  //   });

  //   const content = await this.taplogRepository.find({
  //     relations: {
  //       member: true,
  //       shift: true,
  //     },
  //     where: {
  //       datetime: Between(startDate, endDate),
  //     },
  //   });

  //   // console.log(content);

  //   function groupByMemberCodeAndCardSn(data) {
  //     return data.reduce((acc, item) => {
  //       const memberCode = item.card.member.code;
  //       const cardSn = item.card.sn;
  //       const memberName =
  //         item.card.member.firstname + ' ' + item.card.member.lastname;
  //       const cardType = item.card.cardType;

  //       if (!acc[memberCode]) {
  //         acc[memberCode] = {};
  //       }

  //       if (!acc[memberCode][cardSn]) {
  //         acc[memberCode][cardSn] = {
  //           memberName,
  //           cardType,
  //           count: 0,
  //           logs: [],
  //         };
  //       }

  //       acc[memberCode][cardSn].count += 1;
  //       acc[memberCode][cardSn].logs.push(item);

  //       return acc;
  //     }, {});
  //   }

  //   const groupdata = groupByMemberCodeAndCardSn(content);

  //   // for (const memberCode in groupdata) {
  //   //     for (const cardSn in groupdata[memberCode]) {
  //   //         const logs = groupdata[memberCode][cardSn].logs;
  //   //         console.log(`Logs for memberCode ${memberCode}, cardSn ${cardSn}:`, logs);
  //   //     }
  //   // }

  //   const formatdate = Object.keys(groupdata).reduce((result, memberCode) => {
  //     Object.keys(groupdata[memberCode]).forEach((cardSn) => {
  //       const entry = groupdata[memberCode][cardSn];
  //       const transformedLogs = entry.logs.map((log) => {
  //         const logDate = new Date(log.datetime);
  //         const formattedDate = `${('0' + (logDate.getMonth() + 1)).slice(-2)}/${('0' + logDate.getDate()).slice(-2)}/${logDate.getFullYear()}`;

  //         // Determine shift based on time
  //         let shift = '';
  //         const time = logDate.toTimeString().split(' ')[0];
  //         if (time >= '10:30:00' && time <= '14:00:00') {
  //           shift = 'Lunch Shift1';
  //         } else if (time >= '23:00:00' || time <= '02:00:00') {
  //           shift = 'Lunch Shift2';
  //         }

  //         return {
  //           ...log,
  //           formattedDate,
  //           shift,
  //         };
  //       });

  //       const transformedEntry = {
  //         card: cardSn,
  //         code: memberCode,
  //         name: entry.memberName,
  //         cardType: entry.cardType,
  //         tapCount: entry.count,
  //         logs: transformedLogs,
  //       };
  //       result.push(transformedEntry);
  //     });
  //     return result;
  //   }, []);

  //   // formatdate.forEach(entry => {
  //   //     console.log(`Logs for memberCode ${entry.code}, cardSn ${entry.card}:`);
  //   //     entry.logs.forEach(log => {
  //   //         console.log(`  Log ID: ${log.id}, Date: ${log.formattedDate}, Shift: ${log.shift}`);
  //   //         // Add more properties if needed
  //   //     });
  //   // });

  //   //legacy code
  //   const startDate1 = '10:30:00';
  //   const endDate1 = '14:00:00';
  //   const startDate2 = '23:00:00';
  //   const endDate2 = '02:00:00';

  //   const groupedData = content.reduce((acc, log) => {
  //     const logDate = new Date(log.datetime);
  //     const dateKey = logDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  //     if (!acc[dateKey]) {
  //       acc[dateKey] = {
  //         'subsidy lunch shift 1': 0,
  //         'subsidy lunch shift 2': 0,
  //       };
  //     }
  //     const time = logDate.toTimeString().split(' ')[0]; // Format as HH:MM:SS
  //     // Shift 1: Between startDate1 and endDate1
  //     if (time >= startDate1 && time <= endDate1) {
  //       acc[dateKey]['subsidy lunch shift 1'] += 1;
  //     }
  //     // Shift 2: Between startDate2 and endDate2, taking into account the midnight crossing
  //     else {
  //       const logHour = logDate.getHours();
  //       const logMinute = logDate.getMinutes();
  //       const startHour2 = parseInt(startDate2.split(':')[0], 10);
  //       const endHour2 = parseInt(endDate2.split(':')[0], 10);
  //       const endMinute2 = parseInt(endDate2.split(':')[1], 10);
  //       // Check if log time is between startDate2 and midnight
  //       if (
  //         logHour > startHour2 ||
  //         (logHour === startHour2 &&
  //           logMinute >= parseInt(startDate2.split(':')[1], 10))
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //       // Check if log time is between midnight and endDate2
  //       else if (
  //         logHour < endHour2 ||
  //         (logHour === endHour2 && logMinute <= endMinute2)
  //       ) {
  //         acc[dateKey]['subsidy lunch shift 2'] += 1;
  //       }
  //     }
  //     return acc;
  //   }, {});
  //   // Convert the grouped data object to the desired array format with "date" key
  //   const rawGroupedData = Object.keys(groupedData).map((date) => ({
  //     date: date,
  //     shift1: {
  //       key: 'subsidy lunch shift 1',
  //       value: groupedData[date]['subsidy lunch shift 1'],
  //     },
  //     shift2: {
  //       key: 'subsidy lunch shift 2',
  //       value: groupedData[date]['subsidy lunch shift 2'],
  //     },
  //   }));

  //   const formattedData = rawGroupedData.flatMap((item) => [
  //     {
  //       date: item.date,
  //       description: item.shift1.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift1.value,
  //       amount: item.shift1.value * 76,
  //       vat: item.shift1.value * 76 * 0.07,
  //       totalAmount: item.shift1.value * 76 + item.shift1.value * 76 * 0.07,
  //     },
  //     {
  //       date: item.date,
  //       description: item.shift2.key.replace(
  //         /subsidy lunch /i,
  //         'Subsidy Lunch ',
  //       ),
  //       tabCount: item.shift2.value,
  //       amount: item.shift2.value * 76,
  //       vat: item.shift2.value * 76 * 0.07,
  //       totalAmount: item.shift2.value * 76 + item.shift2.value * 76 * 0.07,
  //     },
  //   ]);
  //   let lastDate = ''; // ตัวแปรช่วยเพื่อเก็บวันที่ล่าสุดที่ได้เพิ่มเข้าไป
  //   const data = formatdate;

  //   data.forEach((item, index) => {
  //     item.logs.forEach((log, logIndex) => {
  //       const rowData = {
  //         card: logIndex === 0 ? item.card : undefined,
  //         code: logIndex === 0 ? item.code : undefined,
  //         name: logIndex === 0 ? item.name : undefined,
  //         cardType: logIndex === 0 ? item.cardType : undefined,
  //         shift: log.shift,
  //         date: log.formattedDate,
  //         tapCount: '1', // Static tapCount based on your requirement
  //       };

  //       const row = worksheet.addRow(rowData);

  //       // Update lastDate if it's not already the same as log.formattedDate
  //       if (logIndex === 0 && log.formattedDate !== lastDate) {
  //         lastDate = log.formattedDate;
  //       }
  //       row.eachCell((cell, colNumber) => {
  //         if (colNumber > 1) {
  //           cell.font = RECORD_FONT_STYLE;
  //           cell.alignment = RECORD_ALIGNMENT;
  //         }
  //       });

  //       // Apply borders to each cell in the row
  //       // row.eachCell((cell) => {
  //       //     cell.border = {
  //       //         top: { style: 'thin', color: { argb: '000000' } },
  //       //         left: { style: 'thin', color: { argb: '000000' } },
  //       //         bottom: { style: 'thin', color: { argb: '000000' } },
  //       //         right: { style: 'thin', color: { argb: '000000' } }
  //       //     };
  //       // });
  //     });
  //     const TotalRow = worksheet.addRow([
  //       '',
  //       '',
  //       '',
  //       '',
  //       '',
  //       '',
  //       'Total',
  //       item.tapCount,
  //       // data.reduce((acc, cur) => acc + cur.tabCount, 0),
  //       // data.reduce((acc, cur) => acc + cur.amount, 0),
  //       // data.reduce((acc, cur) => acc + cur.vat, 0),
  //       // data.reduce((acc, cur) => acc + cur.totalAmount, 0),
  //     ]);

  //     TotalRow.eachCell((cell, index) => {
  //       if (index > 1) {
  //         cell.fill = SUBTOTAL_FILL_STYLE;
  //         cell.font = SUBTOTAL_FONT_STYLE;
  //         cell.alignment = TITLE_ALIGNMENT;
  //       }
  //     });
  //   });
  //   const startDateFormatted: string = DateTime.fromJSDate(startDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const endDateFormatted: string = DateTime.fromJSDate(endDate, {
  //     zone: 'utc',
  //   }).toFormat('dd/MM/yyyy');
  //   const nowDate = DateTime.now()
  //     .setLocale('th-TH')
  //     .toLocaleString({ ...DateTime.DATE_SHORT, month: '2-digit' });
  //   const combinedDate = `${startDateFormatted} ถึงวันที่ ${endDateFormatted}`;
  //   worksheet.insertRow(1, ['']);

  //   worksheet.insertRow(2, ['', 'บริษัทโซเด็กซ์โซ่ อมตะ เซอร์วิสเซส จำกัด']);
  //   worksheet.mergeCells('B2:H2');
  //   worksheet.getCell('B2').font = { size: 14, bold: true };
  //   worksheet.getCell('B2').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(3, ['', 'รายงานสรุปยอดรวมการใช้สิทธิพนักงาน']);
  //   worksheet.mergeCells('B3:H3');
  //   worksheet.getCell('B3').font = { size: 14, bold: true };
  //   worksheet.getCell('B3').alignment = {
  //     horizontal: 'center',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('B3').fill = {
  //     type: 'pattern',
  //     pattern: 'solid',
  //     bgColor: { argb: 'D3D3D3' },
  //     fgColor: { argb: 'D3D3D3' },
  //   };

  //   worksheet.insertRow(4, ['']);
  //   worksheet.mergeCells('B4:F4');

  //   worksheet.insertRow(5, ['', 'ชื่อสาขา', 'Essilor', 'รหัสสาขา', '14']);
  //   worksheet.getCell('B5').font = { size: 11, bold: true };
  //   worksheet.getCell('B5').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D5').font = { size: 11, bold: true };
  //   worksheet.getCell('D5').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(6, ['', 'ชื่อพนักงาน', 'ALL', 'รหัสพนักงาน', 'ALL']);
  //   worksheet.getCell('B6').font = { size: 11, bold: true };
  //   worksheet.getCell('B6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D6').font = { size: 11, bold: true };
  //   worksheet.getCell('D6').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(7, [
  //     '',
  //     'ประจำวันที่',
  //     startDateFormatted,
  //     'ถึงวันที่',
  //     endDateFormatted,
  //   ]);
  //   worksheet.getCell('B7').font = { size: 11, bold: true };
  //   worksheet.getCell('B7').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('D7').font = { size: 11, bold: true };
  //   worksheet.getCell('D7').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };

  //   worksheet.insertRow(8, [
  //     '',
  //     'ข้อมูลแบบ',
  //     'Detail',
  //     '',
  //     '',
  //     '',
  //     'พิมพ์',
  //     nowDate,
  //   ]);
  //   worksheet.getCell('B8').font = { size: 11, bold: true };
  //   worksheet.getCell('B8').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.getCell('G8').font = { size: 11, bold: true };
  //   worksheet.getCell('G8').alignment = {
  //     horizontal: 'left',
  //     vertical: 'middle',
  //   };
  //   worksheet.insertRow(9, ['']);

  //   // Add subtotals row manually if needed
  //   const subTotalRow = worksheet.addRow([
  //     '',
  //     '',
  //     '',
  //     '',
  //     '',
  //     '',
  //     'Sub Total',
  //     data.reduce((acc, cur) => acc + cur.tapCount, 0),
  //     // data.reduce((acc, cur) => acc + cur.tabCount, 0),
  //     // data.reduce((acc, cur) => acc + cur.amount, 0),
  //     // data.reduce((acc, cur) => acc + cur.vat, 0),
  //     // data.reduce((acc, cur) => acc + cur.totalAmount, 0),
  //   ]);

  //   subTotalRow.eachCell((cell, index) => {
  //     if (index > 1) {
  //       cell.fill = {
  //         type: 'pattern',
  //         pattern: 'solid',
  //         bgColor: { argb: 'ADD8E6' },
  //         fgColor: { argb: 'ADD8E6' },
  //       };
  //       cell.font = TITLE_FONT_STYLE;
  //       cell.alignment = TITLE_ALIGNMENT;
  //     }
  //   });
  //   // Write to buffer
  // }
}
