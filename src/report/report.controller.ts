import {
  Controller,
  StreamableFile,
  Header,
  Post,
  Res,
  Body,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { ReportService } from './report.service';
import { Readable } from 'stream';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import {
  auditlog,
  CreditChange,
  GetBuffetByTypeDto,
  GetExpensesDto,
  getOrderdeviceDto,
  getOrderDeviceDto,
  getOrderDto,
  getOrderReserveDto,
  HistoryReportMemberDto,
  ReportOrderDto,
  SaleOrderSummaryDto,
  SalesReportDto,
  Top10Dto,
} from './dto/report-order.dto';
import { DateTime } from 'luxon';
import { ReportTabCardService } from './report-tab-card.service';
import { ReportTapcardDto } from './dto/report-tapcard.dto';
import { ReportTopupDto } from './dto/report-topup.dto';
import { ReportStartEndDateDto } from './dto/report-start-end-date.dto';
import { Repository } from 'typeorm';
import { Order } from 'src/order/entities/order.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Request } from 'express';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('report')
@ApiTags('รายงาน')
export class ReportController {
  constructor(
    private readonly reportService: ReportService,
    private readonly reportTabCardService: ReportTabCardService,
    @InjectRepository(Order) private orderRepository: Repository<Order>,
  ) {}
  // @ApiOperation({ summary: 'รายงานข้อมูลบุฟเฟ่' })
  // @Post('/buffet-export')
  // async exportBuffetData(
  //   @Body() exportDto: GetBuffetByTypeDto,
  //   @Res() res: Response,
  // ) {
  //   const { foodType, startDate, endDate } = exportDto;
  //   const buffer = await this.reportService.getBuffetByType(
  //     foodType,
  //     new Date(startDate),
  //     endDate ? new Date(endDate) : undefined,
  //   );
  //   res.setHeader(
  //     'Content-Disposition',
  //     'attachment; filename=buffet_data.xlsx',
  //   );
  //   res.setHeader(
  //     'Content-Type',
  //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //   );
  //   res.send(buffer);
  // }

  // @Post('/order/excel')
  // async reportOrder(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const content: any = await this.reportService.reportOrder(start, end);

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @Post('/order/excel/cashier')
  // async reportOrderCashier(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const content: any = await this.reportService.reportOrderCashier(
  //     start,
  //     end,
  //   );

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order-cashier.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายงานข้อมูลจำนวนบิลต่อวัน' })
  // @Post('/order/excel/branch')
  // async reportOrderBranch(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const content: any = await this.reportService.reportOrderBranch(start, end);

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order-cashier.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายยอดขายแยกตามกลุ่มสินค้า' })
  // @Post('/order/excel/branch-category')
  // async reportOrderBranchCategory(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;

  //   const content: any = await this.reportService.reportOrderBranchCategory(
  //     start,
  //     end,
  //     categoryId,
  //   );

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order-branch-category.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายยอดขายแยกตามสินค้า' })
  // @Post('/order/excel/branch-product')
  // async reportOrderBranchProduct(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;
  //   const productId = dto.productID;

  //   const content: any = await this.reportService.reportOrderBranchProduct(
  //     start,
  //     end,
  //     productId,
  //   );

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order-branch-product.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายงานชำระเงินแยกตามประเภท' })
  // @Post('/order/excel/payment-type')
  // async reportOrderType(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;
  //   const productId = dto.productID;

  //   const content: any = await this.reportService.reportOrderType(start, end);

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order-type.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายงานยอดรวม Credit รายวัน' })
  // @Post('/order/excel/credit-date')
  // @Header(
  //   'Content-Type',
  //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // )
  // @Header(
  //   'Content-Disposition',
  //   'attachment; filename="report-credit-date.xlsx"',
  // )
  // async reportCreditPerdate(@Body() dto: ReportOrderDto) {
  //   const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;
  //   const productId = dto.productID;

  //   const content: any = await this.reportService.reportCreditperdate(
  //     start,
  //     end,
  //   );

  //   const file = Readable.from(content);

  //   return new StreamableFile(file);
  // }

  // @Get('/report/order')
  // async report(@Res({ passthrough: true }) res: Response): Promise<StreamableFile> {
  //   // const file = Readable.from(await this.reportService.reportOrder());

  //   const filename = new Date().toLocaleDateString();

  //   res.set({
  //     'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order.xlsx"`,
  //   });
  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายงานสรุปยอดรวมการใช้สิทธิ์พนักงาน' })
  // @Post('/tap-log/excel/summary')
  // async reportTest(
  //   @Body() dto: ReportOrderDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const start = DateTime.fromISO(dto.startDate, { zone: 'utc' })
  //     .startOf('day')
  //     .toJSDate();
  //   const end = DateTime.fromISO(dto.endDate, { zone: 'utc' })
  //     .endOf('day')
  //     .toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;
  //   const productId = dto.productID;

  //   const content: any = await this.reportTabCardService.reportSummaryTapLog(
  //     start,
  //     end,
  //   );

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-summary-tap-log.xlsx"`,
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  @ApiOperation({ summary: 'รายงานประวัติการเติมเงินของแต่ละบัตร' })
  @Post('/excel/top-up-history')
  async reportTopUpHistory(
    @Body() dto: ReportOrderDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const start = DateTime.fromISO(dto.startDate, { zone: 'utc' })
      .startOf('day')
      .toJSDate();
    const end = DateTime.fromISO(dto.endDate, { zone: 'utc' })
      .endOf('day')
      .toJSDate();
    const branchId = dto.branchID;

    const content: any = await this.reportService.reportTopUpHistory(
      start,
      end,
    );
    const file = Readable.from(content);

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="report-summary-tap-log.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition',
    });

    return new StreamableFile(file);
  }

  // @ApiOperation({
  //   summary: 'รายงานสรุปยอดรวมการใช้สิทธิ์พนักงานแยกตามประเภทการ์ด',
  // })
  // @Post('/tap-log/excel/summary/type')
  // @Header(
  //   'Content-Type',
  //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // )
  // @Header(
  //   'Content-Disposition',
  //   'attachment; filename="report-summary-tap-card-type-log.xlsx"',
  // )
  // async reportEmployeeTapCardType(@Body() dto: ReportTapcardDto) {
  //   const start = DateTime.fromISO(dto.startDate, { zone: 'utc' })
  //     .startOf('day')
  //     .toJSDate();
  //   const end = DateTime.fromISO(dto.endDate, { zone: 'utc' })
  //     .endOf('day')
  //     .toJSDate();

  //   const branchId = dto.branchID;
  //   const categoryId = dto.categoryID;
  //   const productId = dto.productID;
  //   const content: any = await this.reportTabCardService.reportSummaryTapCard(
  //     start,
  //     end,
  //   );

  //   const file = Readable.from(content);

  //   return new StreamableFile(file);
  // }

  // @ApiOperation({ summary: 'รายงานยอดขายประจำวัน'})
  // @Post('/report-order-today')
  // @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  // @Header('Content-Disposition', 'attachment; filename="report-order-today.xlsx"')
  // async reportToday(@Body() payload: ReportTapLogDto): Promise<any> {

  //     const content: any = await this.reportService.reportOrderToday(payload);

  //     const file = Readable.from(content);

  //     return new StreamableFile(file);
  // }

  @ApiOperation({ summary: 'รายงานประวัติการเติมเงินแต่ละบัตร' })
  @Post('/payment/topup')
  async reportPaymentTopup(
    @Body() dto: ReportTopupDto,
    @Res({ passthrough: true }) res: Response,
  ) {
    const start = DateTime.fromISO(dto.startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(dto.endDate).endOf('day').toJSDate();

    const content: any = await this.reportService.reportTopup(start, end);
    // return content
    const file = Readable.from(content);

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="report-payment-topup.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition',
    });

    return new StreamableFile(file);
  }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.reportService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateReportDto: UpdateReportDto) {
  //   return this.reportService.update(+id, updateReportDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.reportService.remove(+id);
  // }



  //   @Post('device-history-use')
  // @ApiBody({ type: getOrderDto })
  // async searchDevices(
  //   @Body() getOrderDto: getOrderDto,
  // ) {
  //   const { startDate, endDate } = getOrderDto;
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);

  //   const devices = await this.orderRepository.find({
  //     relations: ['device'],
  //     where: {
  //       device: true,
  //       createdAt: Between(start, end)
  //     }
  //   });

  //   return this.reportService.searchDevices(start, end, devices);
  // }

  // @Post('history-reserve')
  // @ApiBody({ type: getOrderDto })
  // async searchReserve(
  //   @Body() getOrderDto: getOrderDto,
  // ) {
  //   const { startDate, endDate } = getOrderDto;
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);

  //   const reserveOrder = await this.orderRepository.find({
  //     where: {
  //       orderType: OrderType.RESERVE,
  //       createdAt: Between(start, end)
  //     }
  //   });

  //   return this.reportService.searchReserve(start, end, reserveOrder);
  // }

  // @Post('/buffet-report')
  // @ApiBody({ type: GetBuffetByTypeDto })
  // async getBuffetByType(
  //   @Body() getBuffetByTypeDto: GetBuffetByTypeDto,
  // ) {
  //   const { foodType, startDate, endDate } = getBuffetByTypeDto;
  //   const start = new Date(startDate);
  //   const end = endDate ? new Date(endDate) : new Date(start.getFullYear(), start.getMonth(), start.getDate(), 23, 59, 59, 999);

  //   return this.reportService.getBuffetByType(foodType, start, end);
  // }
  @ApiOperation({ summary: 'รายงานยอดขายประจำเครื่อง' })
  @Post('device/history/excel')
  async searchDevicesExcel(
    @Body() getOrderDto: getOrderDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate, deviceId } = getOrderDto;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const excelBuffer = await this.reportService.searchDevicesExcel(
      start,
      end,
      deviceId,
    );

    res.setHeader(
      'Content-Disposition',
      'attachment; filename=device-history.xlsx',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.status(HttpStatus.OK).send(excelBuffer);
  }
  @ApiOperation({ summary: 'รายงานยอดการจองอาหาร' })
  @Post('reserve/history/excel')
  async searchReserveExcel(
    @Body() getOrderReserveDto: getOrderReserveDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate } = getOrderReserveDto;
    const start = new Date(startDate);
    const end = new Date(endDate);

    const buffer = await this.reportService.searchReserveExcel(start, end);

    res.setHeader(
      'Content-Disposition',
      'attachment; filename="ReserveReport.xlsx"',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.send(buffer);
  }

  @ApiOperation({
    summary: 'รายงานแสดงรายการการขาย (ITEM Sale Transaction Report)',
  })
  @Post('sale-order-summary')
  async reportSaleOrder(
    @Body() payload: SaleOrderSummaryDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate, deviceIds, paymentMethods, includeBuffet } =
      payload;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const buffer = await this.reportService.reportSaleOrder(
      start,
      end,
      deviceIds,
      paymentMethods,
      includeBuffet,
    );

    res.setHeader(
      'Content-Disposition',
      'attachment; filename="POS_Item Sale Transaction Report_SiteName.xlsx"',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.send(buffer);
  }

  // @Post('history-order-menu')
  // @ApiOperation({ summary: 'รายงานประวัติการตัดจ่ายเมนูอาหาร' })
  // @Auth()
  // async reportHistoryOrderMenu(
  //   @Body() payload: HistoryReportMemberDto,
  //   @Res({ passthrough: true }) res: Response,
  // ) {
  //   const { startDate, endDate, memberIds } = payload;

  //   const start = DateTime.fromSQL(startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromSQL(endDate).endOf('day').toJSDate();

  //   const content: any = await this.reportService.reportHistoryOrderMenu(
  //     start,
  //     end,
  //     memberIds,
  //   );

  //   const file = Readable.from(content);

  //   res.set({
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': 'attachment; filename="RepLogCardItemSale.xlsx"',
  //     'Access-Control-Expose-Headers': 'Content-Disposition',
  //   });

  //   return new StreamableFile(file);
  // }

  @ApiOperation({ summary: '  รายงาน สรุปยอดแคชเชียร์ (Sales Summary All)' })
  @Post('sale-summary')
  @Auth()
  async reportSaleSummary(
    @Req() req: Request,
    @Body() getOrderDto: getOrderDto,
  ) {
    const { startDate, endDate, deviceId } = getOrderDto;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();
    const userId = req.user['sub'];
    return this.reportService.reportSaleSummary(start, end, deviceId, userId);
  }

  @ApiOperation({ summary: 'รายงานแสดงรายการการขาย 10 อันดับขายดี' })
  @Post('Top10-sale-order-summary')
  async Top10BestSellersReport(
    @Body() payload: Top10Dto,
    @Res() res: Response,
  ) {
    const { startDate, endDate, branchCodes } = payload;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const buffer = await this.reportService.Top10BestSellersReport(
      start,
      end,
      branchCodes,
    );

    res.setHeader(
      'Content-Disposition',
      'attachment; filename="Top10-sale-order-summary.xlsx"',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.send(buffer);
  }
  @ApiOperation({ summary: 'Generate expenses summary report as Excel' })
  @Post('summary/excel')
  async summaryExpensesExcel(
    @Body() getExpensesDto: GetExpensesDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate } = getExpensesDto;
    const start = new Date(startDate);
    const end = new Date(endDate);

    const buffer = await this.reportService.summaryExpensesExcel(start, end);

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="summaryExpenses.xlsx"',
    );
    res.send(buffer);
  }

  //   @ApiOperation({ summary: 'รายงานแสดงรายการการขาย (All data filtered by branches and devices)' })
  // @Post('sales-report')
  // async generateSalesReport(
  //   @Body() payload: SalesReportDto,
  //   @Res() res: Response
  // ) {
  //   const { startDate, endDate, branchCodes, deviceIds } = payload;
  //   const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

  //   const buffer = await this.reportService.generateSalesReport(start, end, branchCodes, deviceIds);

  //   res.setHeader('Content-Disposition', 'attachment; filename="Sales-Report.xlsx"');
  //   res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  //   res.send(buffer);
  // }

  @ApiOperation({
    summary: 'รายงานแสดงรายการการขายสินค้า (Filtered by Product and devices)',
  })
  @Post('sales-report')
  async generateSalesReport(
    @Body() payload: SalesReportDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate, deviceIds } = payload;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const buffer = await this.reportService.generateSalesReport(
      start,
      end,
      deviceIds,
    );

    res.setHeader(
      'Content-Disposition',
      'attachment; filename="Sales-Product-Report.xlsx"',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.send(buffer);
  }

  // @ApiOperation({ summary: 'รายงานการเติมเครดิต' })
  // @Post('credit-change')
  // async reportCreditChange(
  //   @Body() payload: CreditChange,
  //   @Res() res: Response,
  // ) {
  //   const { startDate, endDate, memberIds } = payload;
  //   const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
  //   const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

  //   const buffer = await this.reportService.reportCreditChange(
  //     start,
  //     end,
  //     memberIds,
  //   );

  //   res.setHeader(
  //     'Content-Disposition',
  //     'attachment; filename="Member-Credit-Change-Report.xlsx"',
  //   );
  //   res.setHeader(
  //     'Content-Type',
  //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //   );
  //   res.send(buffer);
  // }

  @ApiOperation({ summary: 'รายงานกิจกรรม' })
  @Post('Auditlog-Activity')
  async reportAuditlog(@Body() payload: auditlog, @Res() res: Response) {
    const { startDate, endDate, userId } = payload;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const buffer = await this.reportService.reportAuditlog(start, end, userId);

    res.setHeader(
      'Content-Disposition',
      'attachment; filename="Audit-log-Report.xlsx"',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.send(buffer);
  }

  @ApiOperation({ summary: 'รายงานสรุปยอดขายแยกตามร้าน' })
  @Post('device-Summery-payment')
  async searchDevicessumeryExcel(
    @Body() getOrderDto: getOrderdeviceDto,
    @Res() res: Response,
  ) {
    const { startDate, endDate, deviceId } = getOrderDto;
    const start = DateTime.fromISO(startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate).endOf('day').toJSDate();

    const excelBuffer = await this.reportService.searchDevicessumeryExcel(
      start,
      end,
      deviceId,
    );

    res.setHeader(
      'Content-Disposition',
      'attachment; filename=device-history.xlsx',
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.status(HttpStatus.OK).send(excelBuffer);
  }
}
