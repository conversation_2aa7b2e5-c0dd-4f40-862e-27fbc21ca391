import { Module } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderModule } from 'src/order/order.module';
import { Branch } from 'src/branch/entities/branch.entity';
import { Order } from 'src/order/entities/order.entity';
import { Shift } from 'src/shift/entities/shift.entity';
import { ReportTabCardService } from './report-tab-card.service';
import { Product } from 'src/product/entities/product.entity';
import { OrderItem } from 'src/order/entities/order-item.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Branch,
      Shift,
      Order,
      Product,
      OrderItem,
      PaymentMethod,
    ]),
    OrderModule,
  ],
  controllers: [ReportController],
  providers: [ReportService, ReportTabCardService, PaymentMethodService],
})
export class ReportModule {}
