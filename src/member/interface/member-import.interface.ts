// import { CardType } from "src/card/entities/card.entity"
import { CardType } from '../entities/member.entity';

export interface MemberImport {
  card_sn: string;
  memberCode: string;
  firstname: string;
  middlename: string;
  lastname: string;
  department: string;
  card_type: CardType;
  credit: number;
  creditlimit: number;
  grade: string;
  active_date: string;
  active: boolean;
}
export interface MemberImportCredit {
  memberCode: string;
  firstname: string;
  middlename: string;
  lastname: string;
  card_type: CardType;
  credit: number;
  creditlimit: number;
  grade: string;
  active_date: number;
  creditEL2: number;
  creditEL4: number;
  year: number;
  month: number;
}
