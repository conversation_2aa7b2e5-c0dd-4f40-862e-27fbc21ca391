import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Member } from './entities/member.entity';
import { AuditLog } from 'src/auditlog/entities/auditlog.entity';
import { AuditlogService } from 'src/auditlog/auditlog.service';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    private auditlogService: AuditlogService,
  ) {}

  // @Cron('0 0 1 * *')
  // async resetCredits() {
  //   const members = await this.memberRepository.find();
  //   for (const member of members) {
  //     const previousCredit = member.credit;
  //     const previousLimitCredit = member.limitcredit;

  //     member.limitcredit = member.defaultcredit;
  //     await this.memberRepository.save(member);

  //     const memberWalletLog = new MemberWalletLog();
  //     memberWalletLog.date = new Date();
  //     memberWalletLog.wallet = member.wallet;
  //     memberWalletLog.creditEL2 = member.creditEL2;
  //     memberWalletLog.creditEL4 = member.creditEL4;
  //     memberWalletLog.previousCredit = previousCredit;
  //     memberWalletLog.previousLimitCredit = previousLimitCredit;
  //     memberWalletLog.member = member;
  //     await this.memberWalletLogRepository.save(memberWalletLog);
  //   }
  //   await this.memberRepository.update({}, { credit: 0 });
  //   const auditLog = new AuditLog();
  //   auditLog.action = 'CREDIT_RESET';
  //   auditLog.description = `Credit Reset By System`;
  //   auditLog.timestamp = new Date();
  //   await this.auditlogService.create(auditLog);
  // }
}
