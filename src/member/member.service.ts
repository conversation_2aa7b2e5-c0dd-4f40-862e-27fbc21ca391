import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Workbook } from 'exceljs';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { Between, DataSource, Not, Repository } from 'typeorm';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { CardType, Member } from './entities/member.entity';
import { OrderService } from 'src/order/order.service';
import { DateTime } from 'luxon';
import * as xlsx from 'xlsx';
import {
  MemberImportCredit,
} from './interface/member-import.interface';
import { datetime2string } from 'src/common/DatetimeUtil';
import { OrderStatus } from 'src/order/entities/order.entity';
import { chain } from 'lodash';
import { AuditlogService } from 'src/auditlog/auditlog.service';

export const MEMBER_PAGINATION_CONFIG: PaginateConfig<Member> = {
  sortableColumns: ['code', 'firstname', 'active'],
  searchableColumns: ['code', 'firstname', 'lastname', 'sn'],
  relations: ['grade', 'store'],
  filterableColumns: {
    'member.card_type': [FilterOperator.EQ],
  },
};

@Injectable()
export class MemberService {
  HEADDER = [
    'card_sn',
    'memberCode',
    'firstname',
    'middlename',
    'lastname',
    'card_type',
    'grade',
    'active_date',
    'active',
  ];
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Member)
    private memberRepository: Repository<Member>,
    private auditlogService: AuditlogService,
    private orderService: OrderService,
  ) { }

  async checkEmployeeCodeExists(code: string, id: number) {
    const exists = id
      ? await this.memberRepository.existsBy({ id: Not(id), code })
      : await this.memberRepository.existsBy({ code });

    if (exists) {
      throw new BadRequestException('Member already exists');
    }
  }

  async create(createMemberDto: CreateMemberDto) {
    await this.checkEmployeeCodeExists(createMemberDto.code, null);

    const memberRepository = this.dataSource.manager.getRepository(Member);

    if (!createMemberDto.cardType) {
      throw new BadRequestException('CardType not found');
    }

    // Create card entity
    // const card = new Card();

    // Create member entity
    const member = new Member();
    member.code = createMemberDto.code;
    member.firstname = createMemberDto.firstname;
    member.middlename = createMemberDto.middlename;
    member.lastname = createMemberDto.lastname;
    member.limitcredit = createMemberDto.limitcredit;
    member.defaultcredit = member.limitcredit;

    member.sn = createMemberDto.cardSN;
    member.cardType = createMemberDto.cardType;
    member.activeDate = createMemberDto.activeDate;
    member.store = { id: createMemberDto.storeId } as any;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Save card and member
      // await queryRunner.manager.save(card); // Save card first
      const newMember = await queryRunner.manager.save(member); // Save member with the associated card

      await queryRunner.commitTransaction();

      return newMember;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException();
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(user?: any) {
    const whereCondition = user?.storeId ? { store: { id: user.storeId } } : {};

    const members = await this.memberRepository.find({
      where: whereCondition,
      relations: ['grade', 'store'], // Include 'grade' and 'store' relation in the query
    });

    // Optionally handle not found scenario
    if (!members || members.length === 0) {
      throw new NotFoundException('No members found');
    }

    return members;
  }

  async findOne(id: number, user?: any) {
    const whereCondition: any = { id: id };
    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const member = await this.memberRepository.findOne({
      where: whereCondition,
      relations: {
        store: true,
        // card: true,
        // grade: true,
      },
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    return member;
  }

  async update(id: number, updateMemberDto: UpdateMemberDto, user?: any) {
    //await this.checkEmployeeCodeExists(updateMemberDto.code, id)
    if (updateMemberDto.cardSN != null) {
      const memberExists = await Member.exists({
        where: {
          id: Not(id),
          sn: updateMemberDto.cardSN,
        },
      });

      if (memberExists) {
        throw new BadRequestException(
          `Card ${updateMemberDto.cardSN} already exists`,
        );
      }
    }

    const whereCondition: any = { id: id };
    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const member = await Member.findOne({
      where: whereCondition,
      relations: ['store'],
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // const card = member.card
      // card.sn = updateMemberDto.cardSN
      // await queryRunner.manager.save(card)
      member.code = updateMemberDto.code;
      member.firstname = updateMemberDto.firstname;
      member.middlename = updateMemberDto.middlename;
      member.lastname = updateMemberDto.lastname;
      member.active = updateMemberDto.active;
      member.sn = updateMemberDto.cardSN || null;
      member.cardType = updateMemberDto.cardType;
      member.activeDate = updateMemberDto.activeDate;

      // member.credit = grade.credit
      member.limitcredit = updateMemberDto.limitcredit;
      member.defaultcredit = updateMemberDto.limitcredit;

      const updateMember = await queryRunner.manager.save(member);

      await queryRunner.commitTransaction();

      return updateMember;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException();
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const member = await this.memberRepository.findOne({
      where: { id: id },
      // relations: { card: true }
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    const nowStr = DateTime.now().toLocal().toFormat('yyyy-MM-dd HH:mm:ss');

    member.code = nowStr + '-' + member.code;
    await member.save();

    // const card = member.card

    await member.softRemove();

    // await card.softRemove()
  }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Member>> {
    return paginate(
      query,
      this.memberRepository,
      {
        ...MEMBER_PAGINATION_CONFIG,
        where: {
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
    );
  }

  async reportMemberByOrder() {
    const workbook = new Workbook();

    const worksheet = workbook.addWorksheet('Data');

    worksheet.columns = [
      { header: 'Order No.', key: 'orderNo' },
      { header: 'Time', key: 'time' },
      { header: 'Card', key: 'sn' },
      { header: 'Code', key: 'code' },
      { header: 'Name', key: 'name' },
      { header: 'Tap count', key: 'count' },
    ];

    const content = await this.orderService.reportOrderToday();

    const data = [];
    for (let index = 0; index < content.length; index++) {
      const order = content[index];

      const dataExists = data.find((e) => e['code'] == order?.member?.code);
      if (dataExists) {
        dataExists['count'] += 1;
      } else {
        const time = DateTime.fromJSDate(order.orderDate)
          .setLocale('th-TH')
          .toLocaleString(DateTime.TIME_24_SIMPLE);

        const name = `${order?.member?.firstname} ${order?.member?.lastname}`;
        data.push({
          orderNo: order?.orderNo,
          time: time,
          sn: order?.member?.sn,
          code: order?.member?.code,
          name: name,
          count: 1,
        });
      }
    }

    data.forEach((val, i, _) => {
      worksheet.addRow(val);
    });

    return workbook.xlsx.writeBuffer();
  }

  async import(file: Express.Multer.File) {
    // const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    // const queryRunner = this.dataSource.createQueryRunner();

    // const sheetName = workbook.SheetNames[0];
    // const sheet = workbook.Sheets[sheetName];
    // const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1 });

    // const actualHeaders: string[] = jsonData[0] as Array<string>;

    // const isValid = this.HEADDER.every((header) =>
    //   actualHeaders.includes(header),
    // );
    // if (!isValid) {
    //   throw new BadRequestException('Header validation failed.');
    // }

    // const jsonDataWithHeaders: MemberImport[] = xlsx.utils.sheet_to_json(sheet);

    // const result = {
    //   create: 0,
    //   update: 0,
    //   ok: 0,
    //   error: 0,
    //   errordetails: [] as string[],
    // };

    // const updatemember = [];
    // const newmember = [];

    // await queryRunner.connect();
    // await queryRunner.startTransaction();

    // try {
    //   for (const member of jsonDataWithHeaders) {
    //     const membercheck = await this.memberRepository.findOneBy({
    //       code: member.memberCode,
    //     });
    //     const checkvali = {
    //       cardSn: member.card_sn,
    //       code: member.memberCode,
    //       firstname: member.firstname,
    //       lastname: member.lastname,
    //       activedate: member.active_date,
    //       active: member.active,
    //     };
    //     console.log(checkvali);
    //     if (!member.memberCode || member.memberCode === undefined) {
    //       result.error += 1;
    //       result.errordetails.push('Please add Membercode');
    //       continue;
    //     } else if (!member.card_type || member.card_type === undefined) {
    //       result.error += 1;
    //       result.errordetails.push('Please add Cardtype');
    //       continue;
    //     } else if (
    //       !['Student', 'Staff', 'Teacher'].includes(member.card_type)
    //     ) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Please add a valid cardType for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     } else if (!member.firstname || member.firstname === undefined) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Firstname should not be empty for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     } else if (!member.lastname || member.lastname === undefined) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Lastname should not be empty for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     } else if (!member.active_date || member.active_date === undefined) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Please add active_date for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     } else if (
    //       member.active === undefined ||
    //       (typeof member.active === 'string' &&
    //         !['True', 'False'].includes(member.active)) ||
    //       (typeof member.active === 'boolean' &&
    //         ![true, false].includes(member.active))
    //     ) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Please add Active status True or False for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     } else if (member.grade === undefined) {
    //       member.grade = null;
    //     } else if (
    //       ![
    //         'G1',
    //         'G2',
    //         'G3',
    //         'G4',
    //         'G5',
    //         'G6',
    //         'G7',
    //         'G8',
    //         'G9',
    //         'G10',
    //         'G11',
    //         'G12',
    //         'S1',
    //         'T1',
    //         null,
    //       ].includes(member.grade)
    //     ) {
    //       result.error += 1;
    //       result.errordetails.push(
    //         `Please add a valid grade for this member ${member.memberCode}`,
    //       );
    //       continue;
    //     }
    //     let grade = null;
    //     if (member.grade) {
    //       grade = await Grade.findOneBy({ code: member.grade });
    //     } else {
    //       if (member.card_type == 'Staff') {
    //         member.card_type = CardType.STAFF;
    //         member.grade = 'S1';
    //       } else if (member.card_type == 'Teacher') {
    //         member.card_type = CardType.TEACHER;
    //         member.grade = 'T1';
    //       } else if (member.card_type == 'Student') {
    //         member.card_type = CardType.STUDENT;
    //         grade = null;
    //       } else {
    //         result.error += 1;
    //         result.errordetails.push(
    //           `Please add valid CardType for this member ${member.memberCode}`,
    //         );
    //         continue;
    //       }
    //     }

    //     if (membercheck) {
    //       const updatedata = {
    //         sn: member.card_sn || null,
    //         code: member.memberCode,
    //         firstname: member.firstname,
    //         middlename: member.middlename,
    //         lastname: member.lastname,
    //         cardType: member.card_type,
    //         grade: grade || null,
    //         activeDate: member.active_date,
    //         active: Boolean(member.active),
    //       };
    //       updatemember.push(updatedata);
    //       result.update += 1;
    //     } else {
    //       const createdata = {
    //         code: member.memberCode,
    //         firstname: member.firstname,
    //         middlename: member.middlename,
    //         lastname: member.lastname,
    //         active: member.active,
    //         sn: member.card_sn,
    //         cardType: member.card_type,
    //         activeDate: member.active_date,
    //         grade: grade || null,
    //         credit: 0,
    //         defaultcredit: grade ? grade.limitcredit : 0,
    //         limitcredit: grade ? grade.limitcredit : 0,
    //       };
    //       newmember.push(createdata);
    //       result.create += 1;
    //     }
    //   }

    //   // Batch create and update
    //   if (newmember.length > 0) {
    //     await this.memberRepository.save(newmember);
    //   }

    //   if (updatemember.length > 0) {
    //     for (const member of updatemember) {
    //       await this.memberRepository.update({ code: member.code }, member);
    //     }
    //   }

    //   await queryRunner.commitTransaction();
    //   result.ok = result.create + result.update;
    //   return result;
    // } catch (err) {
    //   await queryRunner.rollbackTransaction();
    //   console.error(err);
    //   return { status: 'error' };
    // } finally {
    //   await queryRunner.release();
    // }
  }

  async exportCredit() {
    const workbook = new Workbook();

    const worksheet = workbook.addWorksheet('Member');

    const HEADDER = [
      'memberCode',
      'firstname',
      'middlename',
      'lastname',
      'card_type',
      'creditEL2',
      'creditEL4',
      'year',
      'month',
    ];
    worksheet.addRow(HEADDER);

    const content = await Member.find({
      relations: {
        // card: true
      },
    });

    const now = new Date();

    for (const item of content) {
      worksheet.addRow([
        item.code,
        item.firstname,
        item.lastname,
        item.cardType,
        0,
        0,
        now.getFullYear(),
        now.getMonth() + 1,
      ]);
    }

    return workbook.xlsx.writeBuffer();
  }

  async exportMember() {
    const workbook = new Workbook();

    const worksheet = workbook.addWorksheet('Member');

    worksheet.addRow([
      'card_sn',
      'memberCode',
      'firstname',
      'middlename',
      'lastname',
      'card_type',
      'grade',
      'active_date',
      'active',
    ]);
    // worksheet.addRow(['หมายเลขบัตร', 'รหัสพนักงาน', 'ชื่อ', 'ชื่อกลาง', 'นามสกุล', 'ประเภท', 'เกรด', 'วันที่ลงทะเบียน']);
    // Add row with member data
    worksheet.addRow([
      'XXX1234',
      'ABC123',
      'student',
      ' ',
      'student',
      'Student',
      'G1',
      '2024-01-01',
      'TRUE',
    ]);
    worksheet.addRow([
      'XXX1234',
      'ABC123',
      'teacher',
      ' ',
      'teacher',
      'Teacher',
      'T1',
      '2024-01-01',
      'TRUE',
    ]);
    worksheet.addRow([
      'XXX1234',
      'ABC123',
      'staff',
      ' ',
      'staff',
      'Staff',
      'S1',
      '2024-01-01',
      'TRUE',
    ]);
    // worksheet.getRow(1).hidden = true
    // worksheet.getColumn('A').hidden = true

    return workbook.xlsx.writeBuffer();
  }

  async findMember(memberNo: string, user?: any) {
    const whereCondition: any = { code: memberNo };
    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const member = await Member.findOne({
      where: whereCondition,
      relations: ['store'],
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    return member;
  }

  async registerMember(
    memberId: number,
    cardSN: string,
    gradeId: number,
    cardType: CardType,
  ) {
    const checkSn = await Member.findOne({
      where: {
        sn: cardSN,
      },
    });

    if (checkSn) {
      throw new BadRequestException('Card number already');
    }

    const member = await Member.findOne({
      where: {
        id: memberId,
      },
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    member.sn = cardSN;
    member.cardType = cardType;

    await member.save();

    return Member.findOne({
      where: { id: memberId },
    });
  }

  async printPayment(memberId: number, startDate: Date, endDate: Date) {
    const member = await Member.findOne({
      where: {
        id: memberId,
        orders: {
          orderDate: Between(startDate, endDate),
          orderStatus: OrderStatus.COMPLETE,
        },
      },
      relations: {
        orders: {
          device: true,
          // transactions: true,
        },
        // card: true
      },
    });

    const orderGroupByDate = chain(member?.orders)
      .groupBy((order) => datetime2string(order.orderDate).date)
      .map((v, k) => ({
        date: k,
        items: v.map((item) => ({
          orderNo: item.orderNo,
          device: item.device.name,
          // payments: item.transactions.map((t) => ({
          //   walletType: t.walletType,
          //   amount: t.amount,
          // })),
          total: item.grandTotal,
        })),
      }))
      .value();

    return {
      memberNo: member.code,
      firstname: member.firstname,
      lastname: member.lastname,
      cardType: member.cardType,
      startDate: datetime2string(startDate).datetime,
      endDate: datetime2string(endDate).datetime,
      items: orderGroupByDate,
      total: orderGroupByDate.reduce(
        (sum, curr) =>
          sum + curr.items.reduce((sum1, curr1) => sum1 + curr1.total, 0),
        0,
      ),
      creditEL2: member.creditEL2,
      wallet: member.wallet,
    };
  }

  async getBalanceByCardSn(cardSn: string): Promise<{
    member: Member;
    credit: number;
    limitcredit: number;
    availableBalance: number;
  }> {
    const member = await this.memberRepository.findOne({
      where: { sn: cardSn },
      // relations: { member: true }
    });

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    // const member = member.member;

    // if (!member) {
    //   throw new NotFoundException('Member not found');
    // }

    const availableBalance = member.limitcredit - member.credit;

    return {
      member,
      credit: member.credit,
      limitcredit: member.limitcredit,
      availableBalance,
    };
  }
}
