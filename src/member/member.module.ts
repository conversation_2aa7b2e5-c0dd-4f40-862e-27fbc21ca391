import { Module } from '@nestjs/common';
import { MemberService } from './member.service';
import { MemberController } from './member.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Member } from './entities/member.entity';
import { OrderModule } from 'src/order/order.module';
import { AuditLog } from 'src/auditlog/entities/auditlog.entity';
import { AuditlogModule } from 'src/auditlog/auditlog.module';
import { TasksService } from './tasks.service';
import { ScheduleModule } from '@nestjs/schedule';
@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      Member,
      AuditLog,
      TasksService,
    ]),
    OrderModule,
    AuditlogModule,
  ],
  controllers: [MemberController],
  providers: [MemberService, TasksService],
  exports: [MemberService, TypeOrmModule],
})
export class MemberModule {}
