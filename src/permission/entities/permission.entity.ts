import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Role } from '../../role/entities/role.entity';
import {
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  Unique,
} from 'typeorm';
import { PermissionGroup } from './permission-group.entity';

export enum PermissionType {
  USER_READ = 'USER_READ',
  USER_CREATE = 'USER_CREATE',
  USER_UPDATE = 'USER_UPDATE',
  USER_DELETE = 'USER_DELETE',
  STORE_READ = 'STORE_READ',
  STORE_CREATE = 'STORE_CREATE',
  STORE_UPDATE = 'STORE_UPDATE',
  STORE_DELETE = 'STORE_DELETE',
  BRANCH_READ = 'BRANCH_READ',
  BRANCH_CREATE = 'BRANCH_CREATE',
  BRANCH_UPDATE = 'BRANCH_UPDATE',
  BRANCH_DELETE = 'BRANCH_DELETE',
  CATEGORY_READ = 'CATEGORY_READ',
  CATEGORY_CREATE = 'CATEGORY_CREATE',
  CATEGORY_UPDATE = 'CATEGORY_UPDATE',
  CATEGORY_DELETE = 'CATEGORY_DELETE',
  PRODUCT_READ = 'PRODUCT_READ',
  PRODUCT_CREATE = 'PRODUCT_CREATE',
  PRODUCT_UPDATE = 'PRODUCT_UPDATE',
  PRODUCT_DELETE = 'PRODUCT_DELETE',
}

@Entity()
@Unique(['name'])
export class Permission extends CustomBaseEntity {
  @Column({ comment: 'Permission name' })
  @Index({ unique: true })
  name: string;

  @Column({ nullable: true, comment: 'Permission description' })
  description: string;

  @ManyToMany(() => Role, (_) => _.permissions)
  roles: Array<Role>;

  @ManyToOne(() => PermissionGroup, (_) => _.permissions)
  permissionGroup: PermissionGroup;
}
