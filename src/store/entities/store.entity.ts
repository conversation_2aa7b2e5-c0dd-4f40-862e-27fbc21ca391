import { Column, Entity, Index, OneToMany, Unique } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Branch } from '../../branch/entities/branch.entity';
import { Expose } from 'class-transformer';
import { imagePath } from '../../common/helper';
import { User } from '../../user/entities/user.entity';
import { Panel } from '../../panel/entities/panel.entity';
import { Product } from '../../product/entities/product.entity';
import { Category } from '../../category/entities/category.entity';
import { Shift } from '../../shift/entities/shift.entity';
import { Role } from '../../role/entities/role.entity';
import { Device } from '../../device/entities/device.entity';
import { Member } from '../../member/entities/member.entity';
import { Banner } from '../../banner/entities/banner.entity';

@Entity()
@Unique(['code'])
export class Store extends CustomBaseEntity {
  @Column({ comment: 'Unique code for the store' })
  @Index({ unique: true })
  code: string; // Unique code for the store

  @Column({ comment: 'Name of the store' })
  name: string; // Name of the store

  @Column({ nullable: true, comment: 'Address of the store (optional)' })
  address: string; // Address of the store (optional)

  @Column({ nullable: true, comment: 'Logo URL of the store (optional)' })
  logo: string; // Logo URL of the store (optional)

  @Expose()
  get logoUrl(): string {
    return imagePath(this.logo);
  }

  @Column({ nullable: true })
  tax: string;

  @OneToMany(() => Branch, (_) => _.store)
  branchs: Branch[];

  @OneToMany(() => Panel, (_) => _.store)
  panels: Panel[];
  
  @OneToMany(() => User, (_) => _.store)
  users: User[];

  @OneToMany(() => Product, (_) => _.store)
  products: Product[];

  @OneToMany(() => Category, (_) => _.store)
  categories: Category[];

  @OneToMany(() => Role, (_) => _.store)
  roles: Role[];

  @OneToMany(() => Shift, (_) => _.store)
  shifts: Shift[];

  @OneToMany(() => Device, (_) => _.store)
  devices: Device[];

  @OneToMany(() => Member, (_) => _.store)
  members: Member[];

  @OneToMany(() => Banner, (_) => _.store)
  banners: Banner[];
}
