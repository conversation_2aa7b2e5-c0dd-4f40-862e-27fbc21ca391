import { Module, forwardRef } from '@nestjs/common';
import { OrderService } from './order.service';
import { OrderController } from './order.controller';
import { OrderRepository } from './repository/order.repository';
import { ProductModule } from 'src/product/product.module';
import { OrderItemRepository } from './repository/order-item.repository';
import { OrderPaymentRepository } from './repository/order-payment.repository';
import { OrderPaymentController } from './order-payment.controller';
import { OrderPaymentService } from './order-payment.service';
import { PaysolutionModule } from 'src/paysolution/paysolution.module';
import { PaymentModule } from 'src/payment/payment.module';
import { OrderItemAttributeRepository } from './repository/order-item-attribute.repository';
import { OrderItemAttributeValueRepository } from './repository/order-item-attribute-value.repository';
import { OrderDiscountRepository } from './repository/order-discount.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entities/order.entity';
import { Member } from 'src/member/entities/member.entity';
import { ProductService } from 'src/product/product.service';
import { AuditLog } from 'src/auditlog/entities/auditlog.entity';
import { AuditlogService } from 'src/auditlog/auditlog.service';
import { AuditlogModule } from 'src/auditlog/auditlog.module';
import { Device } from 'src/device/entities/device.entity';

@Module({
  imports: [
    forwardRef(() => PaymentModule),
    ProductModule,
    TypeOrmModule.forFeature([
      Order,
      Member,
      ProductService,
      AuditLog,
      Device,
    ]),
    AuditlogModule,
  ],
  controllers: [OrderController, OrderPaymentController],
  providers: [
    OrderService,
    OrderRepository,
    OrderItemRepository,
    OrderPaymentRepository,
    OrderPaymentService,
    OrderItemAttributeRepository,
    OrderItemAttributeValueRepository,
    OrderDiscountRepository,
    AuditlogService,
  ],
  exports: [OrderService],
})
export class OrderModule {}
