import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource } from 'typeorm';
import {
  OrderPayment,
  OrderPaymentStatus,
} from '../entities/order-payment.entity';

@Injectable({ scope: Scope.REQUEST })
export class OrderPaymentRepository extends BaseRepository {
  // public orderPaymentRepository: Repository<OrderPayment>;

  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);

    // this.orderPaymentRepository = this.getRepository(OrderPayment);
  }

  get repository() {
    return this.getRepository(OrderPayment);
  }

  async createOrderPayment(data: OrderPayment) {
    const orderPaymentRepository = this.getRepository(OrderPayment);

    const orderPayment = orderPaymentRepository.create(data);

    await orderPaymentRepository.insert(orderPayment);

    return orderPayment;
  }

  async paidSuccess(orderPaymentId: number) {
    await this.repository.update(orderPaymentId, {
      status: OrderPaymentStatus.SUCCESS,
    });
  }

  async softDelete(ids: number[]) {
    await this.repository.softDelete(ids);
  }
}
