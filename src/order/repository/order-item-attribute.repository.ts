import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource } from 'typeorm';
import { CreateOrderItemAttributeDto } from '../dto/create-order.dto';
import { OrderItemAtribute } from '../entities/order-item-attribute.entity';

@Injectable({ scope: Scope.REQUEST })
export class OrderItemAttributeRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  async create(orderItemId: number, dto: CreateOrderItemAttributeDto) {
    const repository = this.getRepository(OrderItemAtribute);

    const data = repository.create({
      ...dto,
      orderItem: {
        id: orderItemId,
      },
    });

    await repository.insert(data);

    return data;
  }
  get repository() {
    return this.getRepository(OrderItemAtribute);
  }
}
