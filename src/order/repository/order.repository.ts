import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  Scope,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource, Repository } from 'typeorm';
import { Order, OrderStatus } from '../entities/order.entity';
import { Helper } from 'src/common/helper';
import { CreateOrderDto, CreateOrderMoneyDto } from '../dto';
import { Member } from 'src/member/entities/member.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { AuditLog } from 'src/auditlog/entities/auditlog.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { DateTime } from 'luxon';

@Injectable({ scope: Scope.REQUEST })
export class OrderRepository extends BaseRepository {
  constructor(
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
    @InjectRepository(Member)
    private readonly memberRepository: Repository<Member>,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
  ) {
    super(dataSource, req);
  }

  public get repository() {
    return this.getRepository(Order);
  }

  findAll() {
    return this.getRepository(Order).find();
  }

  async create(data: any, userId: number) {
    const orderRepository = this.getRepository(Order);
    const order = orderRepository.create({
      orderNo: await this.generateOrderNum(),
      orderDate: new Date(),
      total: data.total,
      discount: 0,
      orderStatus: OrderStatus.WAIT_PAYMENT,
      grandTotal: data.total,
      // shift: {
      //     id: data.shiftId,
      // },
      device: {
        id: data.deviceId,
      },
      remark: data?.remark,
      member: {
        id: data?.memberId,
      },
      user: {
        id: userId,
      },
      branch: {
        id: data.branchId,
      },
    });

    await orderRepository.insert(order);
    return order;
  }

  async createMoney(data: CreateOrderMoneyDto, member?: Member) {
    const orderRepository = this.getRepository(Order);

    const order = orderRepository.create({
      orderNo: await this.generateOrderNum(),
      orderDate: new Date(),
      total: data.total,
      discount: 0,
      orderStatus: OrderStatus.COMPLETE,
      grandTotal: data.total,
      member: member,
    });

    await orderRepository.insert(order);

    return order;
  }

  async generateOrderNum(): Promise<string> {
    const orderRepository = this.getRepository(Order);

    const lastOrder = await orderRepository.find({
      order: {
        orderDate: 'DESC',
      },
      take: 1,
    });
    let newOrderNo: string;
    const orderNum = '0001';
    const normal = '1';

    if (!lastOrder.length) {
      const formatDateRes = DateTime.now().toLocal().toFormat('yyyyMMdd');

      newOrderNo = formatDateRes + orderNum;
    } else {
      const orderCreationDate = DateTime.fromJSDate(lastOrder[0].orderDate);

      const orderCreationDateStr = orderCreationDate
        .toLocal()
        .toFormat('yyyyMMdd');

      if (Helper.isToday(orderCreationDate.toJSDate())) {
        const lastOrderNo = lastOrder[0].orderNo.slice(9, 13);
        newOrderNo =
          orderCreationDateStr +
          (parseInt(lastOrderNo) + 1).toString().padStart(4, '0');
      } else {
        // firstOrder of day
        const Today = DateTime.now().toLocal().toFormat('yyyyMMdd');
        newOrderNo = Today + orderNum;
      }
    }
    return normal + newOrderNo;
  }

  // async voidBill(id: number) {

  //   const orderRepository = this.getRepository(Order);
  //   const memberRepository = this.getRepository(Member);
  //   const order = await orderRepository.findOne({
  //     where: { id },
  //     relations: ['member']
  // });
  //   if (!order) {
  //     throw new NotFoundException("Order not found");
  //   }

  //   if (order.orderStatus === OrderStatus.VOID) {
  //     throw new BadRequestException("This order already void")
  //   }
  //   const member = await memberRepository.findOneBy({ id: order.member.id });
  //   if (!member) {
  //     throw new NotFoundException("Member not found");
  //   }

  //   order.orderStatus = OrderStatus.VOID

  //   await orderRepository.save(order);

  //   const grandTotal = order.total;
  //   console.log(member.credit)
  //   member.credit -= grandTotal;
  //   console.log(member.credit)

  //   await this.memberRepository.save(member);

  //   return order;
  // }

  async updateDiscount(orderId: number, amount: number) {
    const orderRepository = this.getRepository(Order);

    await orderRepository.update(orderId, {
      discount: amount,
    });
  }
}
