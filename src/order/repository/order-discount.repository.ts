import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource } from 'typeorm';
import { OrderItem } from '../entities/order-item.entity';
import { CreateOrderItemDto } from '../dto/create-order.dto';
import { OrderDiscountDto } from '../dto/create-order-discount.dto';
import { OrderDiscount } from '../entities/order-discount.entity';

@Injectable({ scope: Scope.REQUEST })
export class OrderDiscountRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  async create(orderId: number, dtos: OrderDiscountDto[]) {
    const orderDiscountRepository = this.getRepository(OrderDiscount);

    const orderDiscounts = [];
    for (const dto of dtos) {
      const odd = orderDiscountRepository.create({
        ...dto,
        order: {
          id: orderId,
        },
      });
      orderDiscounts.push(odd);
    }

    await orderDiscountRepository.save(orderDiscounts);
  }
}
