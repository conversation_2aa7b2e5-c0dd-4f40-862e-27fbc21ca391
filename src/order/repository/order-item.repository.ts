import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseRepository } from 'src/common/repository/base-repository';
import { DataSource } from 'typeorm';
import { OrderItem } from '../entities/order-item.entity';
import { CreateOrderItemDto } from '../dto/create-order.dto';

@Injectable({ scope: Scope.REQUEST })
export class OrderItemRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  async createOrderItem(orderId: number, data: CreateOrderItemDto) {
    const orderItemRepository = this.getRepository(OrderItem);

    const orderItem = orderItemRepository.create({
      ...data,
      order: {
        id: orderId,
      },
      product: {
        id: data.productId,
      },
    });

    await orderItemRepository.insert(orderItem);

    return orderItem;
  }

  get repository() {
    return this.getRepository(OrderItem);
  }
}
