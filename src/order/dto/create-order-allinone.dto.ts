import { IsNotEmpty } from 'class-validator';
import { CreateOrderItemDto } from './create-order.dto';
import { OrderPaymentDto } from './create-order-payment.dto';

export class CreateOrderAllinOneDto {
  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  @IsNotEmpty({ message: 'Device is required' })
  readonly deviceId: number;

  @IsNotEmpty({ message: 'branch is required' })
  readonly branchId: number;

  readonly member: string;

  readonly remark: string;

  readonly orderItems: CreateOrderItemDto[];

  readonly paid: number;

  readonly change: number;

  readonly discount: number;

  @IsNotEmpty({ message: 'uuid is required' })
  readonly uuid: string;

  readonly orderPayments: OrderPaymentDto[];
}

export class PaynextDto {
  @IsNotEmpty({ message: 'orderId is required' })
  readonly orderId: number;

  @IsNotEmpty({ message: 'orderpaymentId is required' })
  readonly orderpaymentId: number;

  readonly memberSn: string;

  readonly membercode: string;
}

export class offlineDto {
  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  @IsNotEmpty({ message: 'Device is required' })
  readonly deviceId: number;

  @IsNotEmpty({ message: 'branch is required' })
  readonly branchId: number;

  @IsNotEmpty({ message: 'orderdate is required' })
  readonly orderdate: Date;

  readonly memberSn: string;

  readonly membercode: string;

  readonly remark: string;

  readonly orderItems: CreateOrderItemDto[];

  readonly paid: number;

  readonly change: number;

  readonly discount: number;

  readonly orderPayments: OrderPaymentDto[];
}
