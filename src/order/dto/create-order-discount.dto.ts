import { IsNotEmpty, <PERSON>N<PERSON>ber } from 'class-validator';

export class CreateOrderDiscountDto {
  readonly orderDiscounts: OrderDiscountDto[];
}

export class OrderDiscountDto {
  @IsNotEmpty({ message: 'Name is required' })
  readonly name: string;

  @IsNotEmpty({ message: 'Description is required' })
  readonly description: string;

  @IsNotEmpty({ message: 'Amount is required' })
  @IsNumber()
  readonly amount: number;

  readonly code: string;
}
