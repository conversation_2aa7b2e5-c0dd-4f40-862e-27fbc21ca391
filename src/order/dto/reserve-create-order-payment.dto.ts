import { IsNotEmpty } from 'class-validator';
import { OrderPaymentStatus } from '../entities/order-payment.entity';

export class ReserveCreateOrderPaymentDto {
  readonly paid: number;

  readonly change: number;

  readonly discount: number;

  readonly orderPayments: ReserveOrderPaymentDto[];
}

export class ReserveOrderPaymentDto {
  @IsNotEmpty()
  readonly paymentMethodId: number;

  @IsNotEmpty()
  readonly amount: number;

  readonly remark: string;

  readonly status?: OrderPaymentStatus;
}
