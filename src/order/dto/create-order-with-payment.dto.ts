import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';

export class CreateOrderWithPaymentDto {
  @IsNotEmpty({ message: 'Device is required' })
  readonly deviceId: number;

  @IsNotEmpty({ message: 'Shift is required' })
  readonly shiftId: number;

  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  readonly memberId: number | null = null;

  @IsNotEmpty({ message: 'Date is required' })
  readonly date: Date;

  @ValidateNested()
  @Type(() => OrderItemWithPaymentDto)
  readonly orderItems: OrderItemWithPaymentDto[];

  @IsNotEmpty({ message: 'Payment method is required' })
  readonly paymentMethodId: number;

  @IsNotEmpty({ message: 'Paid is required' })
  readonly paid: number;

  readonly change?: number;

  readonly discount?: number;

  readonly remark?: string;
}

export class OrderItemWithPaymentDto {
  @IsNotEmpty({ message: 'Product is required' })
  readonly productId: number;

  @IsNotEmpty({ message: 'Price is required' })
  readonly price: number;

  @IsNotEmpty({ message: 'Quantity is required' })
  readonly quantity: number;

  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  // readonly attributes: OrderItemAttributeWithPaymentDto[];
}

export class OrderItemAttributeWithPaymentDto {
  readonly attributeName: string;
  readonly total: number;
  readonly attributeValues: OrderItemAttributeValueWithPaymentDto[];
}

export class OrderItemAttributeValueWithPaymentDto {
  readonly attributeValueName: string;
  readonly quantity: number;
  readonly price: number;
  readonly total: number;
}
