import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class OrderPaidDto {
  @IsNotEmpty({ message: 'Payment method is required' })
  @ApiProperty({ description: 'ช่องทางการชำระเงิน' })
  readonly paymentMethodId: number;

  @IsNotEmpty({ message: 'Paid is required' })
  @ApiProperty({ description: 'จำนวนเงินที่ได้รับ'})
  readonly paid: number;

  @ApiProperty({ description: 'จำนวนทอน' })
  readonly change?: number;

  @ApiProperty({ description: 'ส่วนลด' })
  readonly discount?: number;

  readonly remark?: string;
}
