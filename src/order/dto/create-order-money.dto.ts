import { IsNotEmpty, IsNumber } from 'class-validator';
import { CreateOrderItemDto } from './create-order.dto';

export class CreateOrderMoneyDto {
  @IsNotEmpty({ message: 'Id is required' })
  readonly shiftId: number;

  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  @IsNotEmpty({ message: 'Serial number is required' })
  readonly sn: string;

  readonly orderItems: CreateOrderItemDto[];
}
