import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';

export class CreateOrderDto {
  @IsNotEmpty({ message: 'Device is required' })
  readonly deviceId: number;

  @IsNotEmpty({ message: 'Shift is required' })
  readonly shiftId: number;

  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  readonly memberId: number | null = null;

  @IsNotEmpty({ message: 'Date is required' })
  readonly date: Date;

  // @IsNotEmpty({ message: 'branch is required' })
  // readonly branchId: number;

  // readonly remark: string;

  @ValidateNested()
  @Type(() => CreateOrderItemDto)
  readonly orderItems: CreateOrderItemDto[];
}

export class CreateOrderItemDto {
  @IsNotEmpty({ message: 'Product is required' })
  readonly productId: number;

  @IsNotEmpty({ message: 'Price is required' })
  readonly price: number;

  @IsNotEmpty({ message: 'Quantity is required' })
  readonly quantity: number;

  @IsNotEmpty({ message: 'Total is required' })
  readonly total: number;

  readonly attributes: CreateOrderItemAttributeDto[];
}

export class CreateOrderItemAttributeDto {
  readonly attributeName: string;
  readonly total: number;
  readonly attributeValues: CreateOrderItemAttributeValueDto[];
}

export class CreateOrderItemAttributeValueDto {
  readonly attributeValueName: string;
  readonly quantity: number;
  readonly price: number;
  readonly total: number;
}
