import { IsNotEmpty } from 'class-validator';
import { OrderPaymentStatus } from '../entities/order-payment.entity';

export class CreateOrderPaymentDto {
  readonly paid: number;

  readonly change: number;

  readonly discount: number;

  readonly orderPayments: OrderPaymentDto[];
}

export class OrderPaymentDto {
  @IsNotEmpty({ message: 'Id is required' })
  readonly paymentMethodId: number;

  @IsNotEmpty({ message: 'Amount is required' })
  readonly amount: number;

  readonly remark: string;

  readonly status?: OrderPaymentStatus;
}
