import { Body, Controller, Get, Param, Post, Put } from '@nestjs/common';
import { OrderPaymentService } from './order-payment.service';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiTags } from '@nestjs/swagger';

@Controller('order-payment')
@Auth()
@ApiTags('รายการจ่ายเงิน')
export class OrderPaymentController {
  constructor(private orderPaymentService: OrderPaymentService) {}

  @Post(':id/paid')
  paid(@Param('id') id: string, @Body() dto) {
    return this.orderPaymentService.paid(+id);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.orderPaymentService.find(+id);
  }

  // @Put(':id')
  // update(@Param('id') id: string, @Body() dto) {
  //   // return this.orderService.update(+id, updateOrderDto);
  // }
}
