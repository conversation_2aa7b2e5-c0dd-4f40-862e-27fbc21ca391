import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseInterceptors,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  Query,
  Req,
  BadRequestException,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { ORDER_PAGINATION_CONFIG, OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { TransactionInterceptor } from 'src/common/interceptor/transaction.interceptor';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { CreateOrderPaymentDto } from './dto/create-order-payment.dto';
import {
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { CreateOrderDiscountDto } from './dto/create-order-discount.dto';
import { PayCardDto } from './dto/pay-card.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { PayMemberDto } from './dto/pay-member.dto';
import {
  CreateOrderAllinOneDto,
  offlineDto,
  PaynextDto,
} from './dto/create-order-allinone.dto';
import { OrderPaidDto } from './dto/order-paid.dto';
import { CreateOrderWithPaymentDto } from './dto/create-order-with-payment.dto';

@Controller('order')
@ApiTags('สร้างออเดอร์')
@Auth()
@UseInterceptors(TransactionInterceptor)
export class OrderController {
  constructor(private readonly orderService: OrderService) { }

  @Post('/:id/paid')
  @ApiOperation({ summary: 'ชำระเงิน' })
  async orderPaidWithId(@Param('id', ParseIntPipe) orderId: string, @Body() dto: OrderPaidDto) {
    return this.orderService.orderPaid(+orderId, dto);
  }

  // @Post('reserve/import-excel')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiConsumes('multipart/form-data')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       file: {
  //         type: 'string',
  //         format: 'binary',
  //       },
  //     },
  //   },
  // })
  // async importExcel(
  //   @Req() req: any,
  //   @UploadedFile() file: Express.Multer.File,
  // ) {
  //   if (!file) {
  //     throw new BadRequestException('No file uploaded');
  //   }
  //   const userId = req.user['sub'];
  //   return this.orderService.import(file, userId);
  // }

  // @Get('reserve/export-template')
  // @ApiResponse({
  //   status: HttpStatus.OK,
  //   description: 'Export Excel template',
  //   content: {
  //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
  //       schema: {
  //         type: 'string',
  //         format: 'binary',
  //       },
  //     },
  //   },
  // })
  // async exportTemplate(@Res() res: Response) {
  //   const fileBuffer = await this.orderService.exportTemplate();

  //   res.set({
  //     'Content-Disposition': 'attachment; filename="reserve_template.xlsx"',
  //     'Content-Type':
  //       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //   });

  //   res.send(fileBuffer);
  // }

  @Get('/code')
  @ApiOperation({ summary: 'ค้นหาออเดอร์ด้วยรหัส' })
  getOrderByCode(@Query('orderNo') orderNo: string) {
    return this.orderService.getOrderByCode(orderNo);
  }

  // @Post(':id/save-discount')
  // @ApiOperation({ summary: 'บันทึกส่วนลด' })
  // async saveDiscount(
  //   @Req() req: any,
  //   @Param('id', ParseIntPipe) orderId: string,
  //   @Body() dto: CreateOrderDiscountDto,
  // ) {
  //   const userId = req.user['sub'];

  //   return this.orderService.createOrderDiscount(+orderId, dto);
  // }

  // @Post('create')
  // @ApiOperation({ summary: 'สร้างออเดอร์ แบบแตะบัตร' })
  // async createOrderMoney(@Req() req: any, @Body() dto: CreateOrderMoneyDto) {
  //     return this.orderService.createOrderMoney(dto)
  // }

  // @Post(':id/paid/:orderPaymentId')
  // @ApiOperation({ summary: 'ชำระเงินสำเร็จ ใช้กับเงินสด' })
  // // @UseInterceptors(TransactionInterceptor)
  // async orderAllPaid(
  //   @Param('id', ParseIntPipe) orderId: string,
  //   @Param('orderPaymentId', ParseIntPipe) orderPaymentId: string,
  // ) {
  //   await this.orderService.orderPaidWithId(+orderId, +orderPaymentId);

  //   return this.orderService.findOne(+orderId);
  // }

  // @Post(':id/paid-member')
  // @ApiOperation({ summary: 'ชำระเงินด้วยบัตรพนักงาน' })
  // async orderAllPaidMember(
  //   @Param('id', ParseIntPipe) id: string,
  //   @Body() payload: PayCardDto,
  // ) {
  //   return this.orderService.orderPaidMemberWithId(
  //     +id,
  //     payload.sn,
  //     payload.orderPaymentId,
  //   );
  // }

  // @Post(':id/card-paid-member')
  // @ApiOperation({ summary: 'ชำระเงินด้วยบัตรพนักงาน' })
  // async orderPaidMember(
  //   @Param('id', ParseIntPipe) id: string,
  //   @Body() payload: PayCardDto,
  // ) {
  //   return this.orderService.orderPaidMemberWithCard(
  //     +id,
  //     payload.sn,
  //     payload.orderPaymentId,
  //   );
  // }

  // @Post(':id/next-payment')
  // @ApiOperation({ summary: 'ดึงข้อมูลชำระเงิน' })
  // // @UseInterceptors(TransactionInterceptor)
  // createNextOrderPayment(@Param('id', ParseIntPipe) orderId: string) {
  //   return this.orderService.createNextPayment(+orderId);
  // }

  // @Post(':id/payment/:orderPaymentId')
  // @ApiOperation({ summary: 'ชำระเงินออนไลน์' })
  // // @UseInterceptors(TransactionInterceptor)
  // createOnlineOrderPayment(
  //   @Param('id', ParseIntPipe) orderId: string,
  //   @Param('orderPaymentId', ParseIntPipe) orderPaymentId: string,
  // ) {
  //   return this.orderService.createOnlineOrderPayment(
  //     +orderId,
  //     +orderPaymentId,
  //   );
  // }

  // @Post(':id/payment')
  // @ApiOperation({ summary: 'เลือกการชำเงิน' })
  // // @UseInterceptors(TransactionInterceptor)
  // createOrderPayment(
  //   @Param('id', ParseIntPipe) orderId: string,
  //   @Body() dto: CreateOrderPaymentDto,
  // ) {
  //   return this.orderService.createOrderPayment(+orderId, dto);
  // }
  // @Post(':id/paid-member-code')
  // @ApiOperation({ summary: 'ชำระเงินด้วยรหัสพนักงาน' })
  // async orderPaidMemberWithCode(
  //   @Param('id', ParseIntPipe) orderId: string,
  //   @Body() payload: PayMemberDto,
  // ) {
  //   return this.orderService.orderPaidMemberWithcode(
  //     +orderId,
  //     payload.memberCode,
  //     payload.orderPaymentId,
  //   );
  // }
  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(ORDER_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.orderService.datatables(query, user);
  }

  @Post()
  @ApiOperation({ summary: 'สร้างออเดอร์' })
  create(@Req() req: any, @Body() createOrderDto: CreateOrderDto) {
    const user = req.user;

    return this.orderService.create(createOrderDto, user);
  }

  @Post('order-with-payment')
  @ApiOperation({ summary: 'สร้างออเดอร์ และ ชำระเงิน' })
  createOrderWithPayment(@Req() req: any, @Body() createOrderWithPaymentDto: CreateOrderWithPaymentDto) {
    const user = req.user;

    return this.orderService.createOrderWithPayment(createOrderWithPaymentDto, user);
  }

  @Get()
  findAll() {
    return this.orderService.findAll();
  }

  // @Post(':id/void')
  // voidOrder(@Req() req: any, @Param('id') id: string) {
  //   const userId = req.user['sub'];
  //   return this.orderService.voidBill(+id, +userId);
  // }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.orderService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto) {
    return this.orderService.update(+id, updateOrderDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.orderService.remove(+id);
  }

  @Post('createAllinOne/userId')
  async createAllinOneOrder(
    @Req() req: any,
    @Body() createOrderAllinOneDto: CreateOrderAllinOneDto,
  ) {
    const userId = req.user['sub'];
    const result = await this.orderService.AllinOneOrder(
      createOrderAllinOneDto,
      userId,
    );
    return result;
  }

  @Post('Paynext')
  async paynext(@Req() req: any, @Body() paynextDto: PaynextDto) {
    const userId = req.user['sub'];
    const result = await this.orderService.Paynext(paynextDto, userId);
    return result;
  }

  @Post('offline-mode')
  @ApiBody({ type: [offlineDto] })
  async offlineMode(
    @Req() req: any,
    @Body() createOrderAllinOneDtoArray: offlineDto[],
  ) {
    const userId = req.user['sub'];
    const results = await this.orderService.offlineMode(
      createOrderAllinOneDtoArray,
      userId,
    );
    return results;
  }

  //   @Post('online-mode')
  // async onlineMode(
  //   @Req() req: any,
  //   @Body() createOrderAllinOneDtoArray: offlineDto,
  // ) {
  //     const userId = req.user['sub'];
  //     const results = await this.orderService.onlineMode(createOrderAllinOneDtoArray, userId);
  //     return results;
  //   }
}
