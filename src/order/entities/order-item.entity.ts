import { CustomBaseEntity } from '../../common/entities';
import { Product } from '../../product/entities/product.entity';
import { Column, <PERSON>tity, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { Order } from './order.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { OrderItemAtribute } from './order-item-attribute.entity';

@Entity()
export class OrderItem extends CustomBaseEntity {
  @Column({ comment: 'Quantity of the product in the order' })
  quantity: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'Price of the product in the order' })
  price: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'Total price of the product in the order' })
  total: number;

  @OneToMany(() => OrderItemAtribute, (_) => _.orderItem, { cascade: true })
  attributes: OrderItemAtribute[];

  @ManyToOne(() => Product, (p: Product) => p.orderItems)
  product: Product;

  @ManyToOne(() => Order, (o: Order) => o.orderItems, { onDelete: 'CASCADE' })
  order: Order;

  constructor(partial?: Partial<OrderItem>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
