import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, <PERSON>inC<PERSON>umn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Order } from './order.entity';
import { PaymentMethod } from '../../payment-method/entities/payment-method.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Payment } from '../../payment/entities/payment.entity';
import { Product } from '../../product/entities/product.entity';

export enum OrderPaymentStatus {
  WAIT = 'wait',
  SUCCESS = 'success',
  CANCEL = 'cancel',
}

@Entity()
export class OrderPayment extends CustomBaseEntity {
  @ManyToOne(() => PaymentMethod, (pm) => pm.orderPayments)
  @JoinColumn({ name: 'payment_method_id' })
  paymentMethod: PaymentMethod;

  @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), comment: 'Credit used to pay for the order' })
  amount: number;

  @Column({ nullable: true, comment: 'Remark for the order' })
  remark: string;

  @Column({ type: 'enum', enum: OrderPaymentStatus })
  status: OrderPaymentStatus;

  @OneToOne(() => Order, (o: Order) => o.orderPayment)
  order: Order;

  @OneToMany(() => Payment, (_) => _.orderPayment)
  payments: Payment[];
}
