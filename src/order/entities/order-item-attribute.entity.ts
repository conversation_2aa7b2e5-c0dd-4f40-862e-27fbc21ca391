import { CustomBaseEntity } from '../../common/entities';
import { Colum<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { OrderItem } from './order-item.entity';
import { OrderItemAtributeValue } from './order-item-attribute-value.entity';

@Entity()
export class OrderItemAtribute extends CustomBaseEntity {
  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Total price when add Attribute',
  })
  total: number;

  @Column({ name: 'attribute_name', comment: 'Attribute name' })
  attributeName: string;

  @OneToMany(() => OrderItemAtributeValue, (_) => _.orderItemAttribute)
  attributeValues: OrderItemAtributeValue[];

  @ManyToOne(() => OrderItem, (_) => _.attributes)
  @JoinColumn({ name: 'order_item_id' })
  orderItem: OrderItem;

  constructor(partial?: Partial<OrderItemAtribute>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
