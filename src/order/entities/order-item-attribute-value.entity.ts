import { CustomBaseEntity } from '../../common/entities';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { OrderItemAtribute } from './order-item-attribute.entity';

@Entity()
export class OrderItemAtributeValue extends CustomBaseEntity {
  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Price of the item-Attribute-Value',
  })
  price: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Total cost for the item-Attribute-Value',
  })
  total: number;

  @Column({ comment: 'Quantity of the item-Attribute-Value ordered' })
  quantity: number;

  @ManyToOne(() => OrderItemAtribute, (_) => _.attributeValues)
  @JoinColumn({ name: 'order_item_attribute_id' })
  orderItemAttribute: OrderItemAtribute;

  constructor(partial?: Partial<OrderItemAtributeValue>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
