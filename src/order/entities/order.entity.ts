import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { OrderPayment } from './order-payment.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Shift } from '../../shift/entities/shift.entity';
import { OrderItem } from './order-item.entity';
import { OrderDiscount } from './order-discount.entity';
import { Member } from '../../member/entities/member.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Device } from '../../device/entities/device.entity';
import { User } from '../../user/entities/user.entity';

export enum OrderStatus {
  WAIT_PAYMENT = 'wait_payment',
  COMPLETE = 'complete',
  INCOMPLETE = 'incomplete',
  VOID = 'void',
}

@Entity()
export class Order extends CustomBaseEntity {
  @Column({ comment: 'เลขที่ใบเสร็จ' })
  orderNo: string;

  @Column({ comment: 'วันที่ทำรายการ' })
  orderDate: Date;

  @Column({ type: 'enum', enum: OrderStatus, comment: 'สถานะรายการ' })
  orderStatus: OrderStatus;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'จำนวนเงินที่รับ' })
  paid: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'เงินทอน' })
  change: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'ค่าบริการ' })
  serviceCharge: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'ยอดรวม' })
  total: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'ภาษีมูลค่าเพิ่ม' })
  vat: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'ส่วนลด' })
  discount: number;

  @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), comment: 'ยอดสุทธิ' })
  grandTotal: number;

  @Column({ nullable: true, comment: 'หมายเหตุ' })
  remark: string;

  // @Column({ nullable: true})
  // machine: string

  @ManyToOne(() => Shift, (s: Shift) => s.orders)
  shift: Shift;

  @ManyToOne(() => Branch, (s: Branch) => s.orders)
  branch: Branch;

  @OneToMany(() => OrderItem, (ot: OrderItem) => ot.order, { cascade: true })
  orderItems: OrderItem[];

  @OneToOne(() => OrderPayment, (op: OrderPayment) => op.order, { cascade: true })
  @JoinColumn()
  orderPayment: OrderPayment;

  @OneToMany(() => OrderDiscount, (_) => _.order, { cascade: true })
  orderDiscounts: OrderDiscount[];

  @ManyToOne(() => Member, (_) => _.orders)
  member: Member;

  // @OneToMany(() => Transaction, (_) => _.order)
  // transactions: Transaction[];

  @ManyToOne(() => Device, (_) => _.orders, { onDelete: 'CASCADE' })
  device: Device;

  @ManyToOne(() => User, (_) => _.orders, { onDelete: 'CASCADE' })
  user: User;

  convertStatusToThai() {
    if (this.orderStatus == OrderStatus.COMPLETE) {
      return 'สำเร็จ';
    } else if (this.orderStatus == OrderStatus.INCOMPLETE) {
      return 'ไม่สำเร็จ';
    } else if (this.orderStatus == OrderStatus.VOID) {
      return 'ยกเลิก';
    } else if (this.orderStatus == OrderStatus.WAIT_PAYMENT) {
      return 'รอชำระเงิน';
    }
  }
}
