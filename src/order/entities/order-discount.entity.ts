import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { Order } from './order.entity';

// export enum OrderDiscountType {
//   FIX = "fix",
//   VOUCHER = "voucher",
//   POINT = "point",
//   MEMBER = "member",
//   OTHER = "other",
// }

@Entity()
export class OrderDiscount extends CustomBaseEntity {
  @Column({ comment: 'Name of the discount' })
  name: string;

  @Column({ nullable: true, comment: 'Description of the discount' })
  description: string;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'Amount of the discount',
  })
  amount: number;

  @Column({ nullable: true, comment: 'Code for the discount' })
  code: string;

  @ManyToOne(() => Order, (o: Order) => o.orderDiscounts)
  @JoinColumn({ name: 'order_id' })
  order: Order;
}
