import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ReceiptService } from './receipt.service';
import { CreateReceiptDto } from './dto/create-receipt.dto';
import { UpdateReceiptDto, ApproveReceiptDto, CompleteReceiptDto } from './dto/update-receipt.dto';
import { Receipt, ReceiptStatus, ReceiptType } from './entities/receipt.entity';
import { Auth } from '../auth/decorators/auth.decorator';
import { Paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import { Request } from 'express';

@ApiTags('Receipt - ใบรับของ')
@ApiBearerAuth()
@Controller('receipts')
@Auth()
export class ReceiptController {
  constructor(private readonly receiptService: ReceiptService) {}

  @Post()
  @ApiOperation({ 
    summary: 'สร้างใบรับของใหม่',
    description: 'สร้างใบรับของใหม่พร้อมรายการสินค้า'
  })
  @ApiResponse({
    status: 201,
    description: 'สร้างใบรับของสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 400,
    description: 'ข้อมูลไม่ถูกต้อง',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบสาขาหรือสินค้า',
  })
  async create(
    @Body() createReceiptDto: CreateReceiptDto,
    @Req() req: Request,
  ): Promise<Receipt> {
    const user = req.user;

    return this.receiptService.create(createReceiptDto, user);
  }

  @Get()
  @ApiOperation({ 
    summary: 'ดึงรายการใบรับของทั้งหมด',
    description: 'ดึงรายการใบรับของทั้งหมดพร้อมการแบ่งหน้าและการค้นหา'
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ReceiptStatus,
    description: 'กรองตามสถานะ',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ReceiptType,
    description: 'กรองตามประเภท',
  })
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: Number,
    description: 'กรองตามสาขา',
  })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Req() req: Request,
  ): Promise<Paginated<Receipt>> {
    const user = req.user;

    return this.receiptService.datatables(query, user);
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'ดึงข้อมูลใบรับของตาม ID',
    description: 'ดึงข้อมูลใบรับของพร้อมรายการสินค้าทั้งหมด'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Receipt> {
    return this.receiptService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: 'แก้ไขใบรับของ',
    description: 'แก้ไขข้อมูลใบรับของ (ไม่สามารถแก้ไขได้หากเสร็จสิ้นแล้ว)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'แก้ไขสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถแก้ไขได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateReceiptDto: UpdateReceiptDto,
    @Req() req: Request,
  ): Promise<Receipt> {
    const user = req.user;

    return this.receiptService.update(id, updateReceiptDto, user);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'ลบใบรับของ',
    description: 'ลบใบรับของ (ไม่สามารถลบได้หากเสร็จสิ้นแล้ว)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 204,
    description: 'ลบสำเร็จ',
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถลบได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ): Promise<void> {
    const user = req.user;

    return this.receiptService.remove(id, user);
  }

  @Post(':id/approve')
  @ApiOperation({ 
    summary: 'อนุมัติใบรับของ',
    description: 'อนุมัติใบรับของที่มีสถานะรอดำเนินการ'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'อนุมัติสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถอนุมัติได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async approve(
    @Param('id', ParseIntPipe) id: number,
    @Body() approveDto: ApproveReceiptDto,
    @Req() req: Request,
  ): Promise<Receipt> {
    const user = req.user;

    return this.receiptService.approve(id, approveDto, user);
  }

  @Post(':id/complete')
  @ApiOperation({ 
    summary: 'เสร็จสิ้นใบรับของ',
    description: 'เสร็จสิ้นใบรับของและอัปเดตสต็อกสินค้าอัตโนมัติ'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'เสร็จสิ้นสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถเสร็จสิ้นได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async complete(
    @Param('id', ParseIntPipe) id: number,
    @Body() completeDto: CompleteReceiptDto,
    @Req() req: Request,
  ): Promise<Receipt> {
    const user = req.user;

    return this.receiptService.complete(id, completeDto, user);
  }

  @Post(':id/cancel')
  @ApiOperation({ 
    summary: 'ยกเลิกใบรับของ',
    description: 'ยกเลิกใบรับของ (เฉพาะสถานะร่างหรือรอดำเนินการ)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ยกเลิกสำเร็จ',
    type: Receipt,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถยกเลิกได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async cancel(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ): Promise<Receipt> {
    const user = req.user;
    const updateDto: UpdateReceiptDto = { status: ReceiptStatus.CANCELLED };
    return this.receiptService.update(id, updateDto, user);
  }

  @Get(':id/items')
  @ApiOperation({ 
    summary: 'ดึงรายการสินค้าในใบรับของ',
    description: 'ดึงรายการสินค้าทั้งหมดในใบรับของ'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบรับของ',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบรับของ',
  })
  async getReceiptItems(@Param('id', ParseIntPipe) id: number) {
    const receipt = await this.receiptService.findOne(id);
    return receipt.items;
  }
}
