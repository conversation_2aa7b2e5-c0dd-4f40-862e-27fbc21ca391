import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Receipt } from './receipt.entity';
import { Product } from '../../product/entities/product.entity';
import { Inventory } from '../../inventory/entities/inventory.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

@Entity()
export class ReceiptItem extends CustomBaseEntity {
  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนที่รับ'
  })
  quantity: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนต่อหน่วย'
  })
  unitCost: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ราคาต่อหน่วย'
  })
  unitPrice: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'ส่วนลดต่อรายการ'
  })
  discountAmount: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนภาษีต่อรายการ'
  })
  taxAmount: number;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'มูลค่ารวมของรายการ'
  })
  totalAmount: number;

  @Column({
    nullable: true,
    comment: 'หมายเลขแบทช์หรือล็อต'
  })
  batchNo: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'วันหมดอายุสำหรับสินค้าที่เสื่อมเสีย'
  })
  expiryDate: Date;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมสำหรับรายการนี้'
  })
  notes: string;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกก่อนรับของ'
  })
  stockBefore: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกหลังรับของ'
  })
  stockAfter: number;

  // ความสัมพันธ์
  @ManyToOne(() => Receipt, (receipt) => receipt.items)
  @JoinColumn({ name: 'receipt_id' })
  receipt: Receipt;

  @ManyToOne(() => Product, (product) => product.receiptItems)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Inventory, (inventory) => inventory.receiptItems, { nullable: true })
  @JoinColumn({ name: 'inventory_id' })
  inventory: Inventory;

  constructor(partial?: Partial<ReceiptItem>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
