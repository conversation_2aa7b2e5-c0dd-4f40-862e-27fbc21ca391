import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { CreateReceiptDto } from './create-receipt.dto';
import { ReceiptStatus } from '../entities/receipt.entity';

export class UpdateReceiptDto extends PartialType(CreateReceiptDto) {
  @ApiProperty({
    description: 'สถานะของใบรับของ',
    enum: ReceiptStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(ReceiptStatus, { message: 'สถานะใบรับของไม่ถูกต้อง' })
  readonly status?: ReceiptStatus;
}

export class ApproveReceiptDto {
  @ApiProperty({ description: 'หมายเหตุการอนุมัติ', required: false })
  @IsOptional()
  readonly approvalNotes?: string;
}

export class CompleteReceiptDto {
  @ApiProperty({ description: 'หมายเหตุการเสร็จสิ้น', required: false })
  @IsOptional()
  readonly completionNotes?: string;
}
