import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { Order, OrderStatus } from '../order/entities/order.entity';
import { PaymentMethod } from '../payment-method/entities/payment-method.entity';
import { Between, In, Repository } from 'typeorm';
import { Branch } from 'src/branch/entities/branch.entity';
import { Device } from '../device/entities/device.entity';
import { OrderPayment } from '../order/entities/order-payment.entity';
import { emit } from 'process';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepository: Repository<PaymentMethod>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
  ) { }

  async dashboard(branchId: number) {
    const startDateTime = DateTime.local().set({
      hour: 0,
      minute: 0,
      second: 0,
    });
    const endDateTime = DateTime.local().set({
      hour: 23,
      minute: 59,
      second: 0,
    });

    let currentDateTime = startDateTime;
    const dates = [];

    while (currentDateTime <= endDateTime) {
      dates.push({
        datetime: currentDateTime.toISO(),
        value: 4,
      });
      currentDateTime = currentDateTime.plus({ hours: 1 });
    }

    return {
      total: 500,
      bill: 100,
      avg: 500 / 100,
      chart: dates,
      product: [
        { name: 'กาแฟ', value: 10 },
        { name: 'ชาเย็น', value: 5 },
        { name: 'ชาเขียว', value: 20 },
        { name: 'ชามะนาว', value: 23 },
        { name: 'ชาดำ', value: 1 },
      ],
      paymentType: [
        { name: 'เงินสด', value: 400 },
        { name: 'QR', value: 567 },
      ],
    };
  }

  async sumdashboard(branchIds: number[] | null) {
    const now = DateTime.local();
    const currentYear = now.year;

    const startDate = now.minus({ months: 11 }).startOf('month');
    const endDate = now.endOf('month');

    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDate.toJSDate(), endDate.toJSDate()),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
      relations: {
        device: true,
      },
    });

    const monthlyTotals = orders.reduce(
      (acc, order) => {
        const month = DateTime.fromJSDate(order.orderDate).toFormat('yyyy-MM');

        if (!acc[month]) {
          acc[month] = 0;
        }

        acc[month] += order.total;
        return acc;
      },
      {} as { [key: string]: number },
    );

    const monthlyData = Array.from({ length: 12 }, (_, i) => {
      const monthDate = startDate.plus({ months: i });
      const formattedMonth = monthDate.toFormat('yyyy-MM');
      const fullDate =
        monthDate.startOf('month').toFormat('yyyy-MM-dd') + 'T00:00:00';

      return {
        date: fullDate,
        total: monthlyTotals[formattedMonth] || 0,
      };
    });

    return monthlyData;
  }

  async getTop10BestSellers(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ) {
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .toJSDate();

    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDateTime, endDateTime),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
      relations: {
        device: true,
        orderItems: { product: true },
      },
    });

    const productData: {
      [key: string]: { quantity: number; totalSales: number };
    } = {};

    for (const order of orders) {
      for (const item of order.orderItems) {
        const productName = item.product.name;
        if (!productData[productName]) {
          productData[productName] = { quantity: 0, totalSales: 0 };
        }
        productData[productName].quantity += item.quantity;
        productData[productName].totalSales += item.total;
      }
    }

    const allProducts = Object.keys(productData).map((name) => ({
      name,
      quantity: Math.round(productData[name].quantity),
      totalSales: Math.round(productData[name].totalSales),
    }));

    const top10Products = allProducts
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 10);

    return top10Products;
  }

  async getTop10CategorySales(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ) {
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .toJSDate();

    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDateTime, endDateTime),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
      relations: {
        device: true,
        orderItems: { product: { category: true } },
      },
    });

    const categoryData: {
      [key: string]: { quantity: number; totalSales: number };
    } = {};

    for (const order of orders) {
      for (const item of order.orderItems) {
        const categoryName = item.product.category?.name;
        if (!categoryName) continue;

        if (!categoryData[categoryName]) {
          categoryData[categoryName] = { quantity: 0, totalSales: 0 };
        }

        categoryData[categoryName].quantity += item.quantity;
        categoryData[categoryName].totalSales += item.total;
      }
    }

    const allCategories = Object.keys(categoryData).map((name) => ({
      name,
      quantity: Math.round(categoryData[name].quantity),
      totalSales: Math.round(categoryData[name].totalSales),
    }));

    const top10Categories = allCategories
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 10);

    return top10Categories;
  }

  async sumPaymentByMethod(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ) {
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .toJSDate();

    const allPaymentMethods = await this.paymentMethodRepository.find();

    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDateTime, endDateTime),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
      relations: {
        device: true,
        orderPayment: { paymentMethod: true },
      },
    });

    const paymentMethodTotals: { [key: string]: number } = {};

    for (const order of orders) {
      const method = order.orderPayment.paymentMethod?.name;
      if (method) {
        if (!paymentMethodTotals[method]) {
          paymentMethodTotals[method] = 0;
        }
        paymentMethodTotals[method] += order.orderPayment.amount;
      } else {
        console.warn('Missing payment method for order:', order.id);
      }
    }

    const result = allPaymentMethods.map((method) => ({
      name: method.name,
      value: paymentMethodTotals[method.name] || 0,
    }));

    return result;
  }

  async getOverallPaidAndVoidOrderDetails(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ): Promise<{
    productQuantitySold: number;
    paidOrderCount: number;
    totalPaidAmount: number;
    voidOrderCount: number;
  }> {
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .toJSDate();

    const paidOrders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDateTime, endDateTime),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
      relations: {
        orderItems: true,
      },
    });

    let productQuantitySold = 0;
    const paidOrderCount = paidOrders.length;
    let totalPaidAmount = 0;

    for (const order of paidOrders) {
      for (const item of order.orderItems) {
        productQuantitySold += item.quantity;
      }
      totalPaidAmount += order.paid;
    }

    const voidOrderCount = await this.orderRepository.count({
      where: {
        orderStatus: OrderStatus.VOID,
        createdAt: Between(startDateTime, endDateTime),
        ...(branchIds &&
          branchIds.length > 0 && { branch: { id: In(branchIds) } }),
      },
    });

    return {
      productQuantitySold: Math.round(productQuantitySold),
      paidOrderCount: Math.round(paidOrderCount),
      totalPaidAmount: Math.round(totalPaidAmount),
      voidOrderCount: Math.round(voidOrderCount),
    };
  }

  async besttimeseller(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ) {
    // Set the start and end times in the local timezone
    const startDateTime = DateTime.fromJSDate(startDate)
      .set({ hour: 0, minute: 0, second: 0 })
      .setZone('Asia/Bangkok', { keepLocalTime: true })
      .toJSDate();
    const endDateTime = DateTime.fromJSDate(endDate)
      .set({ hour: 23, minute: 59, second: 59 })
      .setZone('Asia/Bangkok', { keepLocalTime: true })
      .toJSDate();

    // Fetch all payment methods to initialize them in the results
    const allPaymentMethods = await this.paymentMethodRepository.find();
    const paymentMethodNames = allPaymentMethods.map((method) => method.name);

    // Fetch orders completed between the start and end times, including payment methods
    const orders = await this.orderRepository.find({
      where: {
        orderStatus: OrderStatus.COMPLETE,
        orderDate: Between(startDateTime, endDateTime),
        ...(branchIds && branchIds.length && { branch: { id: In(branchIds) } }),
      },
      relations: {
        orderPayment: {
          paymentMethod: true,
        },
      },
    });

    // Reduce orders to sum totals, count orders per hour, and aggregate payment methods by date and hour
    const hourlyDataMap = orders.reduce(
      (acc, order) => {
        const orderDateTime = DateTime.fromJSDate(order.orderDate).setZone(
          'Asia/Bangkok',
        );
        const orderHour = orderDateTime.toFormat('HH');
        const orderDate = orderDateTime.toISODate(); // Extract date in ISO format (YYYY-MM-DD)

        // If the date is not yet in the map, initialize it
        if (!acc[orderDate]) {
          acc[orderDate] = {}; // Initialize an object to store hourly data for this date
        }

        // If the hour is not yet in the map for this date, initialize it
        if (!acc[orderDate][orderHour]) {
          acc[orderDate][orderHour] = {
            total: 0,
            count: 0,
            paymentMethods: [],
          };
          // Initialize all payment methods with 0
          paymentMethodNames.forEach((methodName) => {
            acc[orderDate][orderHour].paymentMethods.push({
              name: methodName,
              total: 0,
              count: 0,
            });
          });
        }

        // Sum totals and counts for the specific hour on this date
        acc[orderDate][orderHour].total += order.total;
        acc[orderDate][orderHour].count += 1;

        // Process payment methods for each order
        const paymentMethod = order.orderPayment.paymentMethod?.name || 'Unknown';
        const paymentMethodEntry = acc[orderDate][
          orderHour
        ].paymentMethods.find((pm) => pm.name === paymentMethod);
        if (paymentMethodEntry) {
          paymentMethodEntry.total += order.orderPayment.amount;
          paymentMethodEntry.count += 1;
        }

        return acc;
      },
      {} as {
        [date: string]: {
          [hour: string]: {
            total: number;
            count: number;
            paymentMethods: { name: string; total: number; count: number }[];
          };
        };
      },
    );

    // Merge hourly data across all days
    const mergedHourlyData = Array.from({ length: 24 }, (_, i) => {
      const hour = i.toString().padStart(2, '0'); // Format hour as 'HH'
      let total = 0;
      let count = 0;
      const paymentMethods = paymentMethodNames.map((name) => ({
        name,
        total: 0,
        count: 0,
      }));

      // Aggregate data across all dates for the current hour
      for (const date in hourlyDataMap) {
        if (hourlyDataMap[date][hour]) {
          total += hourlyDataMap[date][hour].total;
          count += hourlyDataMap[date][hour].count;
          hourlyDataMap[date][hour].paymentMethods.forEach((pm) => {
            const paymentMethodEntry = paymentMethods.find(
              (p) => p.name === pm.name,
            );
            if (paymentMethodEntry) {
              paymentMethodEntry.total += pm.total;
              paymentMethodEntry.count += pm.count;
            }
          });
        }
      }
      const currentDate = new Date();
      const formattedDate = currentDate.toISOString().split('T')[0];
      const datetime = `${formattedDate}T${hour}:00:00`; // ISO format without milliseconds or timezone

      return {
        datetime,
        total,
        count,
        paymentMethods,
      };
    });

    return mergedHourlyData;
  }

  async getDashboardAndTopSellers(
    branchIds: number[] | null,
    startDate: Date,
    endDate: Date,
  ) {
    try {
      if (!branchIds) {
        const allBranches = await this.branchRepository.find({
          select: ['id'],
        });
        branchIds = allBranches.map((branch) => branch.id);
      }
      const [
        monthlyData,
        top10Products,
        sumPayment,
        overallPaidAndOrderQuantity,
        top10Category,
        bestTimeSeller,
      ] = await Promise.all([
        this.sumdashboard(branchIds),
        this.getTop10BestSellers(branchIds, startDate, endDate),
        this.sumPaymentByMethod(branchIds, startDate, endDate),
        this.getOverallPaidAndVoidOrderDetails(branchIds, startDate, endDate),
        this.getTop10CategorySales(branchIds, startDate, endDate),
        this.besttimeseller(branchIds, startDate, endDate),
      ]);

      // Return the aggregated data
      return {
        bestTimeSeller,
        monthlyData,
        top10Products,
        sumPayment,
        overallPaidAndOrderQuantity,
        top10Category,
      };
    } catch (error) {
      throw new Error(
        `Failed to retrieve dashboard and top sellers data: ${error.message}`,
      );
    }
  }
}
