import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
} from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { ApiOperation, ApiBody, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import { get } from 'lodash';

@Controller('dashboard')
export class DashboardController {
  constructor(private dashboardService: DashboardService) {}

  @Get()
  dashboard(@Query('branchId') branchId: number) {
    return this.dashboardService.dashboard(branchId);
  }

  @Get('summary')
  @HttpCode(HttpStatus.OK)
  @ApiQuery({ name: 'branchId', required: true, type: [Number] })
  async getSummaryDashboard(@Query('branchId') branchId: number[]) {
    return this.dashboardService.sumdashboard(branchId);
  }

  // @Get('top-5-sellers')
  // @ApiOperation({ summary: 'Get the top 5 best-selling products' })
  // @ApiQuery({
  //   name: 'branchId',
  //   type: Number,
  //   description: 'Branch ID (optional, can be null)',
  //   required: false
  // })
  // @ApiQuery({
  //   name: 'startDate',
  //   type: String,
  //   description: 'Start date in ISO format',
  //   required: true
  // })
  // @ApiQuery({
  //   name: 'endDate',
  //   type: String,
  //   description: 'End date in ISO format',
  //   required: true
  // })
  // async getTop5BestSellers(
  //   @Query('branchId') branchId: number | null,
  //   @Query('startDate') startDate: string,
  //   @Query('endDate') endDate: string
  // ) {
  //     const startDateTime = new Date(startDate);
  //     const endDateTime = new Date(endDate);

  //     const top5Products = await this.dashboardService.getTop5BestSellers(branchId, startDateTime, endDateTime);

  //     return top5Products;
  // }

  @Get('overview')
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: [Number],
    description: 'Array of Branch IDs (optional, can be null)',
  })
  @ApiQuery({
    name: 'startDate',
    required: true,
    type: String,
    description: 'Start date in ISO format (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'endDate',
    required: true,
    type: String,
    description: 'End date in ISO format (YYYY-MM-DD)',
  })
  async getDashboardAndTopSellers(
    @Query('branchId') branchId: number[] | null,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    // Parse the provided date strings into JavaScript Date objects
    const startDateTime = DateTime.fromISO(startDate).toJSDate();
    const endDateTime = DateTime.fromISO(endDate).toJSDate();

    // Call the service method and return its result
    const result = await this.dashboardService.getDashboardAndTopSellers(
      branchId, // Use branchId directly as an array
      startDateTime,
      endDateTime,
    );

    return result;
  }
}
