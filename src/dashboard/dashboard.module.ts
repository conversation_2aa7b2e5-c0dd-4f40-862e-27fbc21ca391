import { Modu<PERSON> } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from 'src/order/entities/order.entity';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { Branch } from 'src/branch/entities/branch.entity';

@Module({
  providers: [DashboardService],
  controllers: [DashboardController],
  imports: [TypeOrmModule.forFeature([Order, PaymentMethod, Branch])],
})
export class DashboardModule {}
