import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Store } from '../../store/entities/store.entity';
import { PanelProduct } from './panel-product.entity';

@Entity()
export class Panel extends CustomBaseEntity {
  @Column()
  name: string;

  @Column({ default: false })
  active: boolean;

  @ManyToOne(() => Store, (_) => _.panels)
  store: Store;

  @OneToMany(() => PanelProduct, (_) => _.panel, { cascade: true })
  panelProducts: PanelProduct[];
}
