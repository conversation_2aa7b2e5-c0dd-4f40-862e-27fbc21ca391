import {
  Column,
  Entity,
  ManyToOne,
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Panel } from './panel.entity';
import { Product } from '../../product/entities/product.entity';

@Entity()
export class PanelProduct extends CustomBaseEntity {
  @Column({ nullable: true })
  color: string;

  @Column()
  sequence: number;

  @ManyToOne(() => Panel, (_) => _.panelProducts, { onDelete: 'CASCADE' })
  panel: Panel;

  @ManyToOne(() => Product, (_) => _.panelProducts)
  product: Product;
}
