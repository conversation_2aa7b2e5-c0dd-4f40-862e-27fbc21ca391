import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty } from 'class-validator';

export class CreatePanelDto {
  @IsNotEmpty({ message: 'Name is required' })
  readonly name: string;

  @IsNotEmpty({ message: 'Panel Products is required' })
  @IsArray()
  @ApiProperty({
    example: [
      { productId: 1, color: '#00ff00', }, { productId: 2, color: '#00ff00', },
      { productId: 3, color: '#00ff00', }, { productId: 4, color: '#00ff00', },
      { productId: 5, color: '#00ff00', }, { productId: 6, color: '#00ff00', },
      { productId: 7, color: '#00ff00', }, { productId: 8, color: '#00ff00', },
      { productId: 9, color: '#00ff00', }, { productId: 10, color: '#00ff00', },
      { productId: 11, color: '#00ff00', }, { productId: 12, color: '#00ff00', },
    ],
  })
  readonly panelProducts: PanelProductDto[];

  readonly active: boolean;
}

export class PanelProductDto {
  @IsNotEmpty({ message: 'Product Id is required' })
  readonly productId: number;

  // @IsNotEmpty({ message: 'Sequence is required' })
  // readonly sequence: number;

  @ApiProperty({ example: '#00ff00' })
  readonly color: string | null;
}