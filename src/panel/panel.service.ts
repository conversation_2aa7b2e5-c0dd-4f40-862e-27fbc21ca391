import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreatePanelDto } from './dto/create-panel.dto';
import { UpdatePanelDto } from './dto/update-panel.dto';
import { Panel } from './entities/panel.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, FindOptionsWhere, Repository } from 'typeorm';
import {
  FilterOperator,
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import * as xlsx from 'xlsx';
import { Branch } from 'src/branch/entities/branch.entity';
import { Product } from 'src/product/entities/product.entity';
import { Device } from 'src/device/entities/device.entity';
import { PanelProduct } from './entities/panel-product.entity';

export const PANEL_PAGINATION_CONFIG: PaginateConfig<Panel> = {
  sortableColumns: ['name'],
  searchableColumns: ['name'],
  relations: {},
  filterableColumns: {
  },
};

@Injectable()
export class PanelService {
  private readonly logger = new Logger(PanelService.name);

  constructor(
    private dataSource: DataSource,
    @InjectRepository(Panel)
    private panelRepository: Repository<Panel>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
  ) { }

  async create(createPanelDto: CreatePanelDto, user: any) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create panel
      const panel = Panel.create({
        name: createPanelDto.name,
        active: createPanelDto.active,
        panelProducts: createPanelDto.panelProducts.map((p, i) => ({
          product: {
            id: p.productId,
          },
          sequence: i + 1,
          color: p?.color,
        })),
        store: {
          id: user?.storeId,
        },
      });

      await queryRunner.manager.save(panel);

      await queryRunner.commitTransaction();

      return { id: panel.id }

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message);
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(data: { storeId?: number }) {
    const panels = await this.panelRepository.find({
      relations: {
        panelProducts: {
          product: true
        }
      },
      where: {
        ...(data?.storeId ? { store: { id: data.storeId } } : {})
      },
    });

    return panels;
  }

  async findOne(id: number) {
    const panel = await this.panelRepository.findOne({
      where: { id },
      relations: {
        panelProducts: {
          product: true,
        },
      },
      order: {
        panelProducts: {
          sequence: 'ASC',
        },
      },
    });

    if (!panel) {
      throw new NotFoundException('panel not found');
    }

    return panel;
  }

  async update(id: number, updatePanelDto: UpdatePanelDto, user?: any) {
    const panel = await this.panelRepository.findOne({
      where: { id },
    });

    if (!panel) {
      throw new NotFoundException('panel not found');
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      await queryRunner.manager.delete(PanelProduct, {
        panel: { id },
      });

      panel.name = updatePanelDto.name
      panel.active = updatePanelDto.active

      panel.panelProducts = updatePanelDto.panelProducts.map((p, i) => ({
        product: {
          id: p.productId,
        },
        sequence: i + 1,
        color: p?.color,
      })) as any;

      await queryRunner.manager.save(panel);

      await queryRunner.commitTransaction();

      return this.panelRepository.findOne({
        where: { id },
      });
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err?.message);
      throw new BadRequestException(err?.message);
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const panel = await this.panelRepository.findOneBy({ id });

    if (!panel) {
      throw new NotFoundException('panel not found');
    }

    await this.panelRepository.remove(panel);
  }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Panel>> {
    return paginate(query, this.panelRepository, {
      ...PANEL_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async import(file: Express.Multer.File) {
    // Parse the buffer
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });

    // Choose the sheet you want to convert
    const sheetName = workbook.SheetNames[0]; // Get the first sheet
    const sheet = workbook.Sheets[sheetName];

    // Convert the sheet to JSON
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1 });

    // Validate headers
    const actualHeaders: string[] = jsonData[0] as Array<string>;

    const isValid = ['panel_name', 'branch_code', 'product_code'].every(
      (header) => actualHeaders.includes(header),
    );
    if (!isValid) {
      throw new BadRequestException('Header validation failed.');
    }

    // Convert the sheet to JSON
    const jsonDataWithHeaders: {
      panel_name: string;
      branch_code: string;
      product_code: string;
    }[] = xlsx.utils.sheet_to_json(sheet);

    const result = {
      create: 0,
      update: 0,
      ok: 0,
      error: 0,
      total: jsonDataWithHeaders.length ?? 0,
    };

    for (const data of jsonDataWithHeaders) {
      //find product
      const product = await Product.findOneBy({ code: data.product_code });

      if (!product) {
        console.info(`product code ${data.product_code} not found.`);
        result.error += 1;
        continue;
      }

      const branch = await Branch.findOneBy({ code: data.branch_code });
      if (!branch) {
        console.info(`branch code ${data.branch_code} not found.`);
        result.error += 1;
        continue;
      }

      //find panel exist
      let panel = await Panel.findOne({
        where: {
          name: data.panel_name,
        },
      });

      if (!panel) {
        panel = await Panel.save({
          name: data.panel_name,
        });
      }

      result.create += 1;
      result.ok += 1;
    }

    return result;
  }

  async layout(ids: number[]) {
    for (let index = 0; index < ids.length; index++) {
      const id = ids[index];
      await Panel.update(id, {});
    }
  }
}
