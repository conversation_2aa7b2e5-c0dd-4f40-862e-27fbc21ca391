import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  HttpCode,
  HttpStatus,
  ClassSerializerInterceptor,
  UseInterceptors,
  Req,
} from '@nestjs/common';
import { PANEL_PAGINATION_CONFIG, PanelService } from './panel.service';
import { CreatePanelDto } from './dto/create-panel.dto';
import { UpdatePanelDto } from './dto/update-panel.dto';
import {
  ApiTags,
} from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Request } from 'express';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('panel')
@ApiTags('หน้าจอขายสินค้า')
@UseInterceptors(ClassSerializerInterceptor)
@Auth()
export class PanelController {
  constructor(private readonly panelService: PanelService) { }

  // @Post('import-panel')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiConsumes('multipart/form-data')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       file: {
  //         type: 'string',
  //         format: 'binary',
  //       },
  //     },
  //   },
  // })
  // importPanel(@UploadedFile() file: Express.Multer.File) {
  //   if (!file) {
  //     throw new BadRequestException('No file uploaded');
  //   }

  //   return this.panelService.import(file);
  // }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PANEL_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    
    const user = req.user;

    return this.panelService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createPanelDto: CreatePanelDto) {
    const user = req.user;

    return this.panelService.create(createPanelDto, user);
  }

  @Get()
  findAll(@Req() req: Request) {
    const storeId = req.user['storeId'];

    return this.panelService.findAll({ storeId: +storeId });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.panelService.findOne(+id);
  }

  @Put(':id')
  update(@Req() req: Request, @Param('id') id: string, @Body() updatePanelDto: UpdatePanelDto) {
    const user = req.user;

    return this.panelService.update(+id, updatePanelDto, user);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.panelService.remove(+id);
  }
}
