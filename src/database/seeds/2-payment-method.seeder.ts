import {
  PaymentMethod,
  PaymentMethodType,
} from '../../payment-method/entities/payment-method.entity';
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';

export default class PaymentMethodSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    const repository = dataSource.getRepository(PaymentMethod);
    
    await repository.insert([
      {
        name: 'เงินสด',
        type: PaymentMethodType.CASH,
      },
      {
        name: 'เคดิต',
        type: PaymentMethodType.CREDIT,
      },
    ]);
  }
}
