import { PermissionGroup } from '../../permission/entities/permission-group.entity';
import { Permission } from '../../permission/entities/permission.entity';
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import { Role } from '../../role/entities/role.entity';

export default class PermissionSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    const permissionGroupRepository = dataSource.getRepository(PermissionGroup);
    const permissionRepository = dataSource.getRepository(Permission);
    const roleRepository = dataSource.getRepository(Role);

    await roleRepository.insert([
      { name: 'Super Admin' },
      { name: 'Admin' },
      { name: 'Supervisor' },
      { name: 'Cashier' },
    ]);

    await permissionGroupRepository.insert([
      { name: 'User' },
      { name: 'Store' },
      { name: '<PERSON>' },
      { name: 'Category' },
      { name: 'Product' },
    ]);

    await permissionRepository.insert([
      { name: 'USER_CREATE', permissionGroup: { id: 1 } },
      { name: 'USER_READ', permissionGroup: { id: 1 } },
      { name: 'USER_UPDATE', permissionGroup: { id: 1 } },
      { name: 'USER_DELETE', permissionGroup: { id: 1 } },
      { name: 'STORE_CREATE', permissionGroup: { id: 2 } },
      { name: 'STORE_READ', permissionGroup: { id: 2 } },
      { name: 'STORE_UPDATE', permissionGroup: { id: 2 } },
      { name: 'STORE_DELETE', permissionGroup: { id: 2 } },
      { name: 'BRANCH_CREATE', permissionGroup: { id: 3 } },
      { name: 'BRANCH_READ', permissionGroup: { id: 3 } },
      { name: 'BRANCH_UPDATE', permissionGroup: { id: 3 } },
      { name: 'BRANCH_DELETE', permissionGroup: { id: 3 } },
      { name: 'CATEGORY_CREATE', permissionGroup: { id: 4 } },
      { name: 'CATEGORY_READ', permissionGroup: { id: 4 } },
      { name: 'CATEGORY_UPDATE', permissionGroup: { id: 4 } },
      { name: 'CATEGORY_DELETE', permissionGroup: { id: 4 } },
      { name: 'PRODUCT_CREATE', permissionGroup: { id: 5 } },
      { name: 'PRODUCT_READ', permissionGroup: { id: 5 } },
      { name: 'PRODUCT_UPDATE', permissionGroup: { id: 5 } },
      { name: 'PRODUCT_DELETE', permissionGroup: { id: 5 } },
    ]);

    let query = '';

    for (let i = 1; i <= 4; i++) {
      for (let j = 1; j <= 20; j++) {
        query += `(${i}, ${j}),`;
      }
    }

    await dataSource.query(`
      INSERT INTO "role_permissions_permission" ("role_id", "permission_id")
      VALUES ${query.slice(0, -1)};
    `);
  }
}
