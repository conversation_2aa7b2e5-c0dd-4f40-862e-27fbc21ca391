import { Product } from "../../product/entities/product.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, ManyToMany } from "typeorm";

@Entity()
export class Vendor extends CustomBaseEntity {
    @Column({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true, length: 10 })
    phone: string;

    @Column({ nullable: true })
    address: string;

    @ManyToMany(() => Product, (product) => product.vendors)
    products: Product[];
}