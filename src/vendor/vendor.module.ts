import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VendorService } from './vendor.service';
import { Vendor } from './entities/vendor.entity';
import { VendorController } from './vendor.controller';

@Module({
    imports: [TypeOrmModule.forFeature([Vendor])],
    controllers: [VendorController],
    providers: [VendorService],
    exports: [VendorService],
})
export class VendorModule { }
