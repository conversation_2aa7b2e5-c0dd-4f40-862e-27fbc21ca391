import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateVendorDto {
    @ApiProperty({ example: 'V0001' })
    @IsNotEmpty()
    @IsString()
    readonly code: string;

    @ApiProperty({ example: 'บริษัท ABC จำกัด' })
    @IsNotEmpty()
    @IsString()
    readonly name: string;

    @ApiProperty({ example: '0812345678' })
    @IsOptional()
    @IsString()
    readonly phone?: string;

    @ApiPropertyOptional({ example: '123 ถนนสุขุมวิท กรุงเทพฯ' })
    @IsOptional()
    @IsString()
    readonly address?: string;
}
