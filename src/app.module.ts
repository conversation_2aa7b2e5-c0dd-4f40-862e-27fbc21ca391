import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user/user.module';
import { RoleModule } from './role/role.module';
import { PermissionModule } from './permission/permission.module';
import { AuthModule } from './auth/auth.module';
import { StoreModule } from './store/store.module';
import { BranchModule } from './branch/branch.module';
import { CategoryModule } from './category/category.module';
import { ProductModule } from './product/product.module';
import { OrderModule } from './order/order.module';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { UploadModule } from './upload/upload.module';
import typeorm from './config/typeorm';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { ShiftModule } from './shift/shift.module';
import { UnitModule } from './unit/unit.module';
import { PaysolutionModule } from './paysolution/paysolution.module';
import { PaymentModule } from './payment/payment.module';
import { PromotionModule } from './promotion/promotion.module';
import { ReportModule } from './report/report.module';
import { PanelModule } from './panel/panel.module';
import { BannerModule } from './banner/banner.module';
import { MemberModule } from './member/member.module';
import { OtaModule } from './ota/ota.module';
import { ScheduleModule } from '@nestjs/schedule';
import { DeviceModule } from './device/device.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { AuditlogModule } from './auditlog/auditlog.module';
import { AppLoggerMiddleware } from './app-logger.middleware';
import { VendorModule } from './vendor/vendor.module';
import { InventoryModule } from './inventory/inventory.module';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [typeorm],
        }),
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, '..', 'uploads'),
        }),
        TypeOrmModule.forRootAsync({
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) =>
                configService.get('typeorm'),
        }),
        ScheduleModule.forRoot(),
        MailerModule.forRootAsync({
            useFactory: () => ({
                transport: {
                    host: process.env.SMTP_HOST,
                    port: parseInt(process.env.SMTP_PORT),
                    auth: {
                        user: process.env.SMTP_USERNAME,
                        pass: process.env.SMTP_PASSWORD,
                    },
                },
                defaults: {
                    from: `"${process.env.SMTP_SENDER}" <${process.env.SMTP_USERNAME}>`,
                },
                template: {
                    dir: process.cwd() + '/templates/',
                    adapter: new HandlebarsAdapter(),
                    options: {
                        strict: true,
                    },
                },
            }),
        }),
        AuthModule,
        UserModule,
        RoleModule,
        PermissionModule,
        StoreModule,
        BranchModule,
        CategoryModule,
        ProductModule,
        OrderModule,
        PaymentMethodModule,
        UploadModule,
        ShiftModule,
        UnitModule,
        PaysolutionModule,
        PaymentModule,
        PromotionModule,
        ReportModule,
        PanelModule,
        BannerModule,
        MemberModule,
        OtaModule,
        DeviceModule,
        DashboardModule,
        AuditlogModule,
        VendorModule,
        InventoryModule
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer.apply(AppLoggerMiddleware).forRoutes('/**');
    }
}
