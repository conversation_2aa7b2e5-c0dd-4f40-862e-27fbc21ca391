import { CustomBaseEntity } from '../../common/entities';
import { Store } from '../../store/entities/store.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Unique,
} from 'typeorm';
import { Shift } from '../../shift/entities/shift.entity';
import { Product } from '../../product/entities/product.entity';
import { User } from '../../user/entities/user.entity';
import { PaymentMethod } from '../../payment-method/entities/payment-method.entity';
import { Order } from '../../order/entities/order.entity';
import { Device } from '../../device/entities/device.entity';
import { Panel } from '../../panel/entities/panel.entity';
import { Inventory } from '../../inventory/entities/inventory.entity';

@Entity()
export class Branch extends CustomBaseEntity {
  @Column({ comment: 'Unique code for the Branch' })
  @Index()
  code: string;

  // Name of the Branch
  @Column({ comment: 'Name of the Branch' })
  name: string;

  // Address related to the Branch (optional)
  @Column({ nullable: true, comment: 'Address of the Branch (optional)' })
  address: string;

  // Description of the Branch (optional)
  @Column({ nullable: true, comment: 'Description of the Branch (optional)' })
  description: string;

  // Indicates whether the Branch is active
  @Column({ default: false, comment: 'Indicates whether the Branch is active' })
  active: boolean;

  @ManyToOne(() => Store, (_) => _.branchs)
  store: Store;

  @ManyToMany(() => User, (_) => _.branchs)
  @JoinTable()
  users: User[];

  @ManyToMany(() => Product, (product) => product.branches)
  products: Product[];

  @OneToMany(() => Order, (_) => _.branch)
  orders: Order[];

  @OneToMany(() => Inventory, (inventory) => inventory.branch)
  inventories: Inventory[];
}
