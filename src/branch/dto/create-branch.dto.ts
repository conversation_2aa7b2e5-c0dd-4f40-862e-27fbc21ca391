import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';

export class CreateBranchDto {
  @IsNotEmpty({ message: 'Code is required' })
  @ApiProperty()
  readonly code: string;

  @IsNotEmpty({ message: 'Name is required' })
  @ApiProperty()
  readonly name: string;

  @ApiProperty({ required: false })
  readonly address: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'Active is required' })
  @IsBoolean()
  active: boolean;

  // @IsNotEmpty({ message: 'StoreId is required' })
  // @ApiProperty()
  // readonly storeId: number;
}
