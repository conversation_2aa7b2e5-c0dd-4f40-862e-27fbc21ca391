import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { BRANCH_PAGINATION_CONFIG, BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Request } from 'express';

@Controller('branch')
@ApiTags('สาขา')
@Auth()
export class BranchController {
  constructor(private readonly branchService: BranchService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(BRANCH_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.branchService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createBranchDto: CreateBranchDto) {
    const user = req.user;

    return this.branchService.create(createBranchDto, user);
  }

  @Get()
  findAll(@Req() req: Request) {
    const user = req.user;

    return this.branchService.findAll(user);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.branchService.findOne(+id);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: string,
    @Body() updateBranchDto: UpdateBranchDto,
  ) {
    return this.branchService.update(+id, updateBranchDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.branchService.remove(+id);
  }
}
