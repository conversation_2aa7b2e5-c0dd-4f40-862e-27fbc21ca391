import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
  Query,
} from '@nestjs/common';
import { OtaService } from './ota.service';
import { CreateOtaDto } from './dto/create-ota.dto';
import { UpdateOtaDto } from './dto/update-ota.dto';
import { OtaDevice } from './entities/ota.entity';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('ota')
@ApiTags('OTA')
@Auth()
export class OtaController {
  constructor(private readonly otaService: OtaService) {}

  @Post()
  create(@Body() createOtaDto: CreateOtaDto) {
    return this.otaService.create(createOtaDto);
  }

  @Get('/pos')
  findPos() {
    return this.otaService.findWithBranchId(OtaDevice.POS);
  }

  @Get('/kiosk')
  findKiosk() {
    return this.otaService.findWithBranchId(OtaDevice.KIOSK);
  }

  @Get()
  findAll() {
    return this.otaService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.otaService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateOtaDto: UpdateOtaDto) {
    return this.otaService.update(+id, updateOtaDto);
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.otaService.remove(+id);
  // }
}
