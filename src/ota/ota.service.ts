import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateOtaDto } from './dto/create-ota.dto';
import { UpdateOtaDto } from './dto/update-ota.dto';
import { Ota, OtaDevice } from './entities/ota.entity';

@Injectable()
export class OtaService {
  constructor() {}

  create(createOtaDto: CreateOtaDto) {
    const ota = Ota.create({
      ...createOtaDto,
    });

    return ota.save();
  }

  findAll() {
    return Ota.find();
  }

  async findOne(id: number) {
    const ota = await Ota.findOne({
      where: {
        id: id,
      },
    });

    if (!ota) {
      throw new NotFoundException(`Ota ${id} not found.`);
    }

    return ota;
  }

  async update(id: number, updateOtaDto: UpdateOtaDto) {
    const ota = await Ota.findOne({
      where: {
        id: id,
      },
    });

    if (!ota) {
      throw new NotFoundException(`Ota ${id} not found.`);
    }

    await Ota.update(id, {
      url: updateOtaDto?.url,
      filename: updateOtaDto?.filename,
      checksum: updateOtaDto?.checksum,
      device: updateOtaDto?.device,
    });
  }

  async remove(id: number) {
    const ota = await Ota.findOneBy({ id: id });

    if (!ota) {
      throw new NotFoundException(`Ota ${id} not found.`);
    }

    await ota.remove();
  }

  async findWithBranchId(device: OtaDevice) {
    const ota = await Ota.findOne({
      where: {
        device: device,
      },
    });

    if (!ota) {
      throw new NotFoundException(`Ota not found.`);
    }

    return ota;
  }
}
