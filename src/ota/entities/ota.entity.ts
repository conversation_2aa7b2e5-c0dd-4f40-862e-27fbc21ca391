import { Branch } from '../../branch/entities/branch.entity';
import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

export enum OtaDevice {
  POS = 'POS',
  KIOSK = 'KIOSK',
}

@Entity()
export class Ota extends CustomBaseEntity {
  @Column()
  url: string;

  @Column()
  filename: string;

  @Column()
  checksum: string;

  @Column()
  device: string;
}
