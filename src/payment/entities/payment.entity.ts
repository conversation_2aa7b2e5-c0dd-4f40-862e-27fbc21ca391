import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { OrderPayment } from '../../order/entities/order-payment.entity';
import { Member } from '../../member/entities/member.entity';
// import { Transaction } from '../../transaction/entities/transaction.entity';

export enum PaymentType {
  PAID = 'PAID',
  TOPUP = 'TOPUP',
}

@Entity()
export class Payment extends CustomBaseEntity {
  @Column({
    type: 'int8',
    name: 'reference_no',
    unique: true,
    comment: 'Unique reference number for the payment',
  })
  referenceNo: number; // Unique reference number for the payment

  @Column({ comment: 'Payment provider used for the transaction' })
  provider: string; // Payment provider used for the transaction

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'Total amount of the payment',
  })
  total: number; // Total amount of the payment

  @Column({ comment: 'Current status of the payment' })
  status: string; // Current status of the payment

  @Column({
    nullable: true,
    comment: 'Date when the payment expires (optional)',
  })
  expiredate?: Date; // Date when the payment expires (optional)

  @Column('text', {
    nullable: true,
    comment: 'Raw request data sent for the payment (optional)',
  })
  rawrequest?: string; // Raw request data sent for the payment (optional)

  @Column('text', {
    nullable: true,
    comment: 'Raw response data received from the payment provider (optional)',
  })
  rawreponse?: string; // Raw response data received from the payment provider (optional)

  @Column('text', {
    nullable: true,
    comment:
      'Callback information received from the payment provider (optional)',
  })
  callback?: string; // Callback information received from the payment provider (optional)

  @Column({ type: 'enum', enum: PaymentType, nullable: true })
  paymentType: PaymentType;

  @ManyToOne(() => OrderPayment, (_) => _.payments)
  @JoinColumn({ name: 'order_payment_id' })
  orderPayment: OrderPayment;

  @ManyToOne(() => Member, (_) => _.payments)
  @JoinColumn({ name: 'member_id' })
  member: Member;

  // @OneToOne(() => Transaction, (_) => _.payment)
  // transaction: Transaction;

  constructor(partial?: Partial<Payment>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
