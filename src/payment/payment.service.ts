import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { PaysolutionService } from 'src/paysolution/paysolution.service';
import {
  OrderPayment,
  OrderPaymentStatus,
} from 'src/order/entities/order-payment.entity';
import { Payment, PaymentType } from './entities/payment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { Helper } from 'src/common/helper';
import { lastValueFrom } from 'rxjs';
import { PaysolutionCallback } from 'src/paysolution/paysolution.dto';
import { OrderService } from 'src/order/order.service';
import { TopupDto } from './dto/topup.dto';
import { Member, CardType } from 'src/member/entities/member.entity';
// import { CardType } from 'src/card/entities/card.entity';

import {
  PaginateConfig,
  FilterOperator,
  paginate,
  PaginateQuery,
  Paginated,
} from 'nestjs-paginate';

export const PAYMENT_PAGINATION_CONFIG: PaginateConfig<Payment> = {
  sortableColumns: [
    'id',
    'referenceNo',
    'provider',
    'total',
    'status',
    'expiredate',
    'paymentType',
  ],
  relations: {
    orderPayment: {
      order: true,
    },
    member: true,
    // transaction: true,
  },
  withDeleted: true,
  searchableColumns: ['provider', 'status', 'paymentType'],
  defaultSortBy: [['expiredate', 'DESC']],
  filterableColumns: {
    referenceNo: [FilterOperator.EQ],
    total: [FilterOperator.GTE, FilterOperator.LTE],
    status: [FilterOperator.EQ],
    expiredate: [FilterOperator.BTW],
    paymentType: [FilterOperator.EQ],
    'orderPayment.order.orderNo': [FilterOperator.EQ], // You can search for Order number
  },
};

@Injectable()
export class PaymentService {
  constructor(
    private paysolutionService: PaysolutionService,
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    @Inject(forwardRef(() => OrderService))
    private orderService: OrderService,
  ) {}

  async datatables(query: PaginateQuery): Promise<Paginated<Payment>> {
    return paginate(query, this.paymentRepository, PAYMENT_PAGINATION_CONFIG);
  }

  async createPayment(orderPayment: OrderPayment) {
    // get last payment number of day
    // const today = new Date();
    // today.setHours(0, 0, 0, 0);

    // const startOfDay = new Date(today);
    // const endOfDay = new Date(today);
    // endOfDay.setDate(endOfDay.getDate() + 1);

    // const lastPayment = await this.paymentRepository.findOne({
    //   where: [
    //     { createdAt: Between(startOfDay, endOfDay)},
    //   ],
    //   order: { createdAt: 'DESC' }
    // });

    const payment = new Payment();

    payment.referenceNo = +Helper.generate12DigitDateString();
    payment.provider = orderPayment.paymentMethod.type;
    payment.total = orderPayment.amount;
    payment.status = 'created';
    payment.orderPayment = orderPayment;
    payment.paymentType = PaymentType.PAID;

    let image = null;

    try {
      const resp = await lastValueFrom(
        this.paysolutionService.createThaiQR(
          'กาแฟ',
          'upos',
          payment.total,
          payment.referenceNo,
        ),
      );
      payment.rawrequest = JSON.stringify(resp.config);
      payment.rawreponse = JSON.stringify(resp.data);
      payment.expiredate = resp.data.data.expiredate;
      image = resp.data.data.image;
      await this.paymentRepository.save(payment);
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }

    return {
      refNo: payment.referenceNo,
      total: payment.total,
      expiredate: payment.expiredate,
      image: image,
    };
  }

  async createPaymentTopup(payload: TopupDto) {
    const member = await Member.findOne({
      where: {
        sn: payload.card,
      },
      relations: {
        // card: true
      },
    });

    if (!member) {
      throw new NotFoundException('Member not found.');
    }

    if (member.cardType == CardType.TEACHER) {
      throw new BadRequestException('ประเภทบัตรของคุณไม่สามารถใช้เงินได้');
    }

    const payment = new Payment();

    payment.referenceNo = +Helper.generate12DigitDateString();
    payment.provider = 'thaiqr';
    payment.total = payload.amount;
    payment.status = 'created';
    payment.paymentType = PaymentType.TOPUP;
    payment.member = member;

    let image = null;

    try {
      const resp = await lastValueFrom(
        this.paysolutionService.createThaiQR(
          'เติมเงิน',
          'topup',
          payment.total,
          payment.referenceNo,
        ),
      );
      payment.rawrequest = JSON.stringify(resp.config);
      payment.rawreponse = JSON.stringify(resp.data);
      payment.expiredate = resp.data.data.expiredate;
      image = resp.data.data.image;
      await this.paymentRepository.save(payment);
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error);
    }

    return {
      refNo: payment.referenceNo,
      total: payment.total,
      expiredate: payment.expiredate,
      image: image,
    };
  }

  async callbackPaysulotion(data: PaysolutionCallback) {
    const payment = await this.paymentRepository.findOne({
      relations: {
        orderPayment: {
          order: true,
        },
        member: true,
      },
      where: {
        referenceNo: +data.refno,
      },
    });

    if (!payment) {
      throw new NotFoundException('payment not found');
    }

    payment.status = 'success';
    payment.callback = JSON.stringify(data);

    await payment.save();

    if (payment.paymentType == PaymentType.PAID) {
      const orderPayment = payment.orderPayment;
      orderPayment.status = OrderPaymentStatus.SUCCESS;
      await orderPayment.save();

      await this.orderService.isOrderPaidAll(orderPayment.order.id);
    } else if (payment.paymentType == PaymentType.TOPUP) {
      const member = payment.member;

      member.wallet += payment.total;
      await member.save();

      // const transaction = Transaction.create({
      //   date: new Date(),
      //   amount: payment.total,
      //   type: TransactionType.TOPUP,
      //   channel: TransactionChannel.QR,
      //   description: `Top up with QR amount: ${payment.total}`,
      //   member: member,
      //   payment: payment,
      //   walletType: WalletType.WALLET,
      // });

      // await transaction.save();
    }
  }

  async inqueryRefno(refNo: string) {
    const payment = await this.paymentRepository.findOne({
      where: {
        referenceNo: +refNo,
      },
    });

    if (!payment) {
      throw new NotFoundException('Payment not found.');
    }

    return payment;
  }
}
