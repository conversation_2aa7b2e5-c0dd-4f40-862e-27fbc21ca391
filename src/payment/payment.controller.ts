import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { PAYMENT_PAGINATION_CONFIG, PaymentService } from './payment.service';
import { PaysolutionCallback } from 'src/paysolution/paysolution.dto';
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { TopupDto } from './dto/topup.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('payment')
@ApiTags('Payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('/paysolution')
  @ApiConsumes('application/x-www-form-urlencoded')
  @ApiConsumes('application/json')
  callback(@Body() body: PaysolutionCallback) {
    // console.log(JSON.stringify(body));
    return this.paymentService.callbackPaysulotion(body);
  }

  @Post('/topup')
  @ApiOperation({ summary: 'เติมเงิน' })
  @ApiConsumes('application/json')
  topup(@Body() payload: TopupDto) {
    console.log(JSON.stringify(payload));
    return this.paymentService.createPaymentTopup(payload);
  }

  @Get('/inquire')
  @ApiOperation({ summary: 'ครวจสอบการเติมเงิน' })
  inquire(@Query('refNo') refNo: string) {
    console.log(refNo);
    return this.paymentService.inqueryRefno(refNo);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PAYMENT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.paymentService.datatables(query);
  }

  // @Get()
  // findAll() {
  //   return this.paymentService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.paymentService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updatePaymentDto: UpdatePaymentDto) {
  //   return this.paymentService.update(+id, updatePaymentDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.paymentService.remove(+id);
  // }
}
