import { Product } from '../../product/entities/product.entity';
import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity()
export class Unit extends CustomBaseEntity {
  @Column({ nullable: true, comment: 'Unique code for the unit' })
  code: string; // Unique code for the unit (optional)

  @Column({ comment: 'Name of the unit' })
  name: string; // Name of the unit

  @OneToMany(() => Product, (_) => _.unit)
  products: Array<Product>;

  constructor(partial?: Partial<Unit>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
