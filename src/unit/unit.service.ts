import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateUnitDto } from './dto/create-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { Unit } from './entities/unit.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';

export const UNIT_PAGINATION_CONFIG: PaginateConfig<Unit> = {
  sortableColumns: ['id', 'name'],
};

@Injectable()
export class UnitService {
  constructor(
    @InjectRepository(Unit)
    private unitRepository: Repository<Unit>,
    private dataSource: DataSource,
  ) {}

  async create(createUnitDto: CreateUnitDto) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // Check if a unit with the same code already exists
      const existingUnit = await this.unitRepository.findOneBy({
        code: createUnitDto.code,
      });

      if (existingUnit) {
        throw new BadRequestException(
          `Unit with code ${createUnitDto.code} already exists`,
        );
      }

      const unit = this.unitRepository.create(createUnitDto);
      await queryRunner.manager.save(unit);

      await queryRunner.commitTransaction();
      return createUnitDto;
    } catch (err) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  findAll() {
    return this.unitRepository.find();
  }

  async findOne(id: number) {
    const unit = await this.unitRepository.findOneBy({ id });

    if (!unit) {
      throw new NotFoundException(`unit #${id} not found`);
    }

    return unit;
  }

  async update(id: number, updateUnitDto: UpdateUnitDto) {
    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const unit = await this.unitRepository.findOneBy({ id });

      if (!unit) {
        throw new NotFoundException(`unit #${id} not found`);
      }

      unit.name = updateUnitDto.name;
      unit.code = updateUnitDto.code;

      await queryRunner.manager.save(unit);

      await queryRunner.commitTransaction();
      return updateUnitDto;
    } catch (err) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const unit = await this.unitRepository.findOneBy({ id });

    if (!unit) {
      throw new NotFoundException(`unit #${id} not found`);
    }

    await this.unitRepository.remove(unit);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Unit>> {
    return paginate(query, this.unitRepository, UNIT_PAGINATION_CONFIG);
  }
}
