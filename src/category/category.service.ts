import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { DateTime } from 'luxon';

export const CATEGORY_PAGINATION_CONFIG: PaginateConfig<Category> = {
  sortableColumns: ['code', 'name'],
};

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private categoryRepository: Repository<Category>,
  ) { }

  create(createCategoryDto: CreateCategoryDto, user: any) {
    const category = this.categoryRepository.create({
      ...createCategoryDto,
      store: {
        id: user?.storeId,
      },
    });

    return this.categoryRepository.save(category);
  }

  findAll(user: any) {
    return this.categoryRepository.find({
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async findOne(id: number) {
    const category = await this.categoryRepository.findOne({
      where: { id },
    });
    if (!category) throw new NotFoundException('category not found');

    return category;
  }

  async update(id: number, updateCategoryDto: UpdateCategoryDto) {
    const category = await this.findById(id);

    if (!category) throw new NotFoundException('category not found');

    return this.categoryRepository.update(id, updateCategoryDto);
  }

  async remove(id: number) {
    const category = await this.findById(id);

    if (!category) throw new NotFoundException('category not found');

    const nowStr = DateTime.now().toLocal().toString();

    category.code = `${nowStr}-${category.code}`;
    await category.save();

    await this.categoryRepository.softRemove(category);
  }

  findById(id: number) {
    return this.categoryRepository.findOneBy({ id });
  }

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Category>> {
    return paginate(query, this.categoryRepository, {
      ...CATEGORY_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }
}
