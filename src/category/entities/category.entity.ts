import { Product } from '../../product/entities/product.entity';
import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { Store } from '../../store/entities/store.entity';

@Entity()
export class Category extends CustomBaseEntity {
  @Column()
  @Index()
  code: string;

  @Column({ comment: 'Category Name' })
  name: string;

  @OneToMany(() => Product, (_) => _.category)
  products: Product[];

  @ManyToOne(() => Store, (_) => _.categories)
  store: Store;
}
