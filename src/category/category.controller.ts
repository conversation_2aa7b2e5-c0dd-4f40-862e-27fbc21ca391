import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  CATEGORY_PAGINATION_CONFIG,
  CategoryService,
} from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Request } from 'express';

@Controller('category')
@ApiTags('หมวดหมู่')
@Auth()
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CATEGORY_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;

    return this.categoryService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createCategoryDto: CreateCategoryDto) {
    const user = req.user;

    return this.categoryService.create(createCategoryDto, user);
  }

  @Get()
  findAll(@Req() req: Request) {
    const user = req.user;
    
    return this.categoryService.findAll(user);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.categoryService.findOne(+id);
  }

  @Put(':id')
  update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ) {
    return this.categoryService.update(+id, updateCategoryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.categoryService.remove(+id);
  }
}
