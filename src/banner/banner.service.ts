import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateBannerDto } from './dto/create-banner.dto';
import { UpdateBannerDto } from './dto/update-banner.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Banner } from './entities/banner.entity';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';

export const BANNER_PAGINATION_CONFIG: PaginateConfig<Banner> = {
  sortableColumns: ['id', 'title', 'description'],
  searchableColumns: ['title', 'description'],
  relations: ['store'],
};

@Injectable()
export class BannerService {
  constructor(
    @InjectRepository(Banner)
    private readonly bannerRepository: Repository<Banner>,
  ) {}

  create(createBannerDto: CreateBannerDto, user?: any) {
    const banner = this.bannerRepository.create({
      ...createBannerDto,
      store: { id: user?.storeId } as any,
    });

    return this.bannerRepository.save(banner);
  }

  findAll(isShow?: boolean, user?: any) {
    const whereCondition: any = {};

    if (isShow !== undefined) {
      whereCondition.isShow = isShow;
    }

    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    return this.bannerRepository.find({
      where: whereCondition,
      relations: ['store'],
    });
  }

  async findOne(id: number, user?: any) {
    const whereCondition: any = { id };

    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const banner = await this.bannerRepository.findOne({
      where: whereCondition,
      relations: ['store'],
    });

    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    return banner;
  }

  async update(id: number, updateBannerDto: UpdateBannerDto, user?: any) {
    const whereCondition: any = { id };

    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const banner = await this.bannerRepository.findOne({
      where: whereCondition,
      relations: ['store'],
    });

    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    return this.bannerRepository.save({ ...banner, ...updateBannerDto });
  }

  async remove(id: number, user?: any) {
    const whereCondition: any = { id };

    if (user?.storeId) {
      whereCondition.store = { id: user.storeId };
    }

    const banner = await this.bannerRepository.findOne({
      where: whereCondition,
      relations: ['store'],
    });

    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    await this.bannerRepository.delete(id);

    return banner;
  }

  async datatables(query: PaginateQuery, user?: any): Promise<Paginated<Banner>> {
    return paginate(
      query,
      this.bannerRepository,
      {
        ...BANNER_PAGINATION_CONFIG,
        where: {
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
    );
  }
}
