import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ClassSerializerInterceptor,
  UseInterceptors,
  Query,
  Put,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { BANNER_PAGINATION_CONFIG, BannerService } from './banner.service';
import { CreateBannerDto } from './dto/create-banner.dto';
import { UpdateBannerDto } from './dto/update-banner.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('banner')
@ApiTags('แบนเนอร์')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class BannerController {
  constructor(private readonly bannerService: BannerService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(BANNER_PAGINATION_CONFIG)
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user as any;
    return this.bannerService.datatables(query, user);
  }

  @Post()
  create(@Req() req: Request, @Body() createBannerDto: CreateBannerDto) {
    const user = req.user as any;
    return this.bannerService.create(createBannerDto, user);
  }

  @Get()
  findAll(@Req() req: Request, @Query('isShow') isShow: string) {
    const user = req.user as any;
    const isShowBoolean = isShow === 'true' ? true : isShow === 'false' ? false : undefined;
    return this.bannerService.findAll(isShowBoolean, user);
  }

  @Get(':id')
  findOne(@Req() req: Request, @Param('id') id: string) {
    const user = req.user as any;
    return this.bannerService.findOne(+id, user);
  }

  @Put(':id')
  update(@Req() req: Request, @Param('id') id: string, @Body() updateBannerDto: UpdateBannerDto) {
    const user = req.user as any;
    return this.bannerService.update(+id, updateBannerDto, user);
  }

  @Delete(':id')
  remove(@Req() req: Request, @Param('id') id: string) {
    const user = req.user as any;
    return this.bannerService.remove(+id, user);
  }
}
