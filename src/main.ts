import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import {
  DocumentBuilder,
  SwaggerCustomOptions,
  SwaggerModule,
} from '@nestjs/swagger';
import { SwaggerTheme, SwaggerThemeNameEnum } from 'swagger-themes';
import { DateTime } from 'luxon';
import { WinstonModule } from 'nest-winston';
import { format, transports } from 'winston';
import 'winston-daily-rotate-file';

async function bootstrap() {
  let app = null;

  if (process.env.NODE_ENV === 'production') {
    app = await NestFactory.create(AppModule, {
      logger: WinstonModule.createLogger({
        transports: [
          // file on daily rotation (error only)
          new transports.DailyRotateFile({
            // %DATE will be replaced by the current date
            filename: `logs/%DATE%-error.log`,
            level: 'error',
            format: format.combine(
              format.timestamp({
                format: () => DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'),
              }),
              format.json(),
            ),
            datePattern: 'YYYY-MM-DD',
            zippedArchive: false, // don't want to zip our logs
            maxFiles: '30d', // will keep log until they are older than 30 days
          }),
          // same for all levels
          new transports.DailyRotateFile({
            filename: `logs/%DATE%-combined.log`,
            format: format.combine(
              format.timestamp({
                format: () => DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'),
              }),
              format.printf((info) => {
                return `${info.timestamp} | ${info.context} | ${info.level} | ${info.body ?? info.message}`;
              }),
            ),
            datePattern: 'YYYY-MM-DD',
            zippedArchive: false,
            maxFiles: '30d',
          }),
          new transports.Console({
            format: format.combine(
              format.cli(),
              format.splat(),
              format.timestamp({
                format: () => DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'),
              }),
              format.printf((info) => {
                return `${info.timestamp} | ${info.context} | ${info.level} | ${info.body ?? info.message}`;
              }),
            ),
          }),
        ],
      }),
    });
  } else {
    app = await NestFactory.create(AppModule);
  }

  app.setGlobalPrefix('api');
  app.useGlobalPipes(new ValidationPipe());

  // const httpAdapterHost  = app.get(HttpAdapterHost);
  // app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost));

  app.enableCors();

  const config = new DocumentBuilder()
    .setTitle('POS ASHA API')
    .setVersion('1.0.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  const theme = new SwaggerTheme();
  const options: SwaggerCustomOptions = {
    explorer: true,
    customCss: theme.getBuffer(SwaggerThemeNameEnum.DARK),
    swaggerOptions: {
      persistAuthorization: true,
    },
  };
  SwaggerModule.setup('swagger', app, document, options);

  await app.listen(process.env.APP_PORT, '0.0.0.0');

  const logger = new Logger('bootstrap');
  logger.log(`Listening on ${await app.getUrl()}`);
}
bootstrap();
