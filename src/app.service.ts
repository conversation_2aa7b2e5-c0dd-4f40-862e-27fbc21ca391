import { Injectable } from '@nestjs/common';
import { Product } from './product/entities/product.entity';
import { Category } from './category/entities/category.entity';
import { Unit } from './unit/entities/unit.entity';
// import { ProductAttribute } from './product/entities/product-attribute.entity';
// import { ProductAttributeValue } from './product/entities/product-attribute-value.entity';
import { Branch } from './branch/entities/branch.entity';
import { Panel } from './panel/entities/panel.entity';
import { PaymentMethod } from './payment-method/entities/payment-method.entity';
import { Member } from './member/entities/member.entity';
import { Device } from './device/entities/device.entity';
import { UserService } from './user/user.service';
import { PanelProduct } from './panel/entities/panel-product.entity';

@Injectable()
export class AppService {
  constructor(
    private usersService: UserService,
  ) { }

  getHello(): string {
    return 'POS API Version 1.0.0';
  }

  async sync(user: any) {
    const userStoreId = user.storeId;

    const [
      branches,
      categories,
      devices,
      members,
      products,
      // productAttributes,
      // productAttributeValues,
      units,
      panels,
      panelProducts,
      paymentMethods,
    ] = await Promise.all([
      Branch.find({ where: { store: { id: userStoreId } }, withDeleted: true }),
      Category.find({ where: { store: { id: userStoreId } }, withDeleted: true }),
      Device.find({
        where: { store: { id: userStoreId } },
        withDeleted: true,
      }),
      Member.find({
        where: { store: { id: userStoreId } },
        withDeleted: true,
      }),
      Product.find({
        where: { store: { id: userStoreId } },
        withDeleted: true,
        relations: {
          category: true,
          unit: true,
        },
      }),
      // ProductAttribute.find({ withDeleted: true, loadRelationIds: { relations: ['product'] } }),
      // ProductAttributeValue.find({ withDeleted: true, loadRelationIds: true }),
      Unit.find({ withDeleted: true }),
      Panel.find({
        where: { store: { id: userStoreId } },
        withDeleted: true,
      }),
      PanelProduct.find({
        where: { panel: { store: { id: userStoreId } } },
        withDeleted: true,
        relations: {
          product: true,
          panel: true,
        },
      }),
      PaymentMethod.find({ withDeleted: true }),
    ]);

    return {
      branches,
      categories,
      devices,
      members,
      products,
      // productAttributes,
      // productAttributeValues,
      units,
      panels,
      panelProducts,
      paymentMethods,
    };
  }
}
