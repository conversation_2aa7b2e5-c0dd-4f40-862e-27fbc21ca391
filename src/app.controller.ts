import { ClassSerializerInterceptor, Controller, Get, Req, UnauthorizedException, UseGuards, UseInterceptors } from '@nestjs/common';
import { AppService } from './app.service';
import { Request } from 'express';
import { ApiHeader } from '@nestjs/swagger';
import { Auth } from './auth/decorators/auth.decorator';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('/ip')
  getYourIP(@Req() req: Request) {
    console.log(req.headers);
  }

  @Auth()
  @Get('/load-data')
  @ApiHeader({ name: 'api-key' })
  @UseInterceptors(ClassSerializerInterceptor)
  async sync(@Req() req: Request) {
    const user = req.user;
    // c7ef38a0594617d91138899ca6f43884724b828047b22a2d16d706d32ed58040
    if (
      req.headers['api-key'] !=
      'c7ef38a0594617d91138899ca6f43884724b828047b22a2d16d706d32ed58040'
    ) {
      throw new UnauthorizedException();
    }

    return this.appService.sync(user);
  }
}
