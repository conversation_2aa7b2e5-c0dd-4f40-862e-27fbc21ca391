import { Injectable } from '@nestjs/common';
import { Upload } from './entities/upload.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { existsSync, unlinkSync } from 'fs-extra';

@Injectable()
export class UploadService {
  constructor(
    @InjectRepository(Upload)
    private uploadRepository: Repository<Upload>,
  ) {}

  createOne(dto: any) {
    return this.uploadRepository.save(dto);
  }

  createMany(dto: any) {
    return this.uploadRepository.insert(dto);
  }

  async remove(id: number) {
    const upload = await this.uploadRepository.findOneBy({ id });

    if (existsSync(upload.path)) {
      unlinkSync(upload.path);
    }

    await this.uploadRepository.delete(id);
  }
}
