import {
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Param,
  Post,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { readFileSync } from 'fs-extra';
import { Helper } from 'src/common/helper';

@Controller('upload')
// @Auth()
// @UseInterceptors(ClassSerializerInterceptor)
export class UploadController {
  constructor(private uploadService: UploadService) {}

  @Post('file')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    const buffer: any = readFileSync(file.path);

    const checksum = Helper.generateChecksum(buffer, 'sha256', 'hex');

    file['sha256Checksum'] = checksum;

    await this.uploadService.createOne(file);

    const _file: any = file;
    _file.pathUrl =
      _file.provider == 'local'
        ? process.env.APP_URL + '/' + _file.filename
        : _file.filename;

    return _file;
  }

  @Post('files')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  async uploadFiles(@UploadedFiles() files: Array<Express.Multer.File>) {
    for (const file of files) {
      const buffer: any = readFileSync(file.path);

      const checksum = Helper.generateChecksum(buffer, 'sha256', 'hex');

      file['sha256Checksum'] = checksum;
    }

    await this.uploadService.createMany(files);

    for (const file of files as any) {
      file.pathUrl =
        file.provider == 'local'
          ? process.env.APP_URL + '/' + file.filename
          : file.filename;
    }

    return files;
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.uploadService.remove(+id);
  }
}
