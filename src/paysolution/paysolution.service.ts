import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { PaysolutionResponse } from './paysolution.dto';
import { Observable, map, of, tap } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class PaysolutionService {
  private isSandbox: boolean;
  private merchantID: number;
  private email: string;
  private authKey: string;

  constructor(private readonly httpService: HttpService) {
    this.isSandbox = process.env.PST_SANDBOX != 'false';
    this.merchantID = +process.env.PST_MERCHANTID;
    this.authKey = process.env.PST_AUTHKEY;
    this.email = process.env.PST_EMAIL;
  }

  createThaiQR(
    productDetail: string,
    customerName: string,
    total: number,
    referenceNo: number,
  ): Observable<AxiosResponse<PaysolutionResponse, any>> {
    const url = 'https://apis.paysolutions.asia/tep/api/v2/promptpay';

    if (this.isSandbox) {
      const AxiosResponse: AxiosResponse = {
        config: null,
        data: {
          status: 'success',
          data: {
            orderNo: '8177677',
            referenceNo: '102403230015',
            total: 210,
            orderdatetime: '2024-03-23 18:52:18',
            expiredate: '2024-03-23 19:02:18',
            image:
              'data:image/png;base64,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',
          },
        },
        headers: null,
        status: 200,
        statusText: 'OK',
      };

      return of(AxiosResponse);
    }

    return this.httpService.post(url, null, {
      headers: {
        Authorization: `Bearer ${this.authKey}`,
        Accept: 'application/json',
      },
      params: {
        merchantID: this.merchantID,
        productDetail: productDetail,
        customerEmail: this.email,
        customerName: customerName,
        total: total,
        referenceNo: referenceNo,
      },
    });
  }
}
