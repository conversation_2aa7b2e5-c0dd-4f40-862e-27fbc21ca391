import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);

  // @Cron('0 5 16 * *')
  // async handleCron() {
  //   this.logger.debug('เริ่มต้นการเติมเงิน');

  //   const now = DateTime.now();
  //   let year = now.year;
  //   let month = now.month;

  //   if (month == 1) {
  //     year = year - 1;
  //     month = 12;
  //   }

  //   const members = await Member.find();

  //   //อัพเดทข้อมูลเดือนเก่าว่าใช้ไปเท่าไหร่ ถ้าไม่ข้อมูลข้อมูลเดือนเก่าจะสร้างขึ้นมาให้
  //   const previousMemberCredits = await MemberCredit.find({
  //     where: {
  //       year: year,
  //       month: month,
  //     },
  //     relations: { member: true },
  //   });

  //   const oldMemberCredits: MemberCredit[] = [];
  //   for (const member of members) {
  //     let memberCredit = previousMemberCredits.find(
  //       (m) => m.member.id == member.id,
  //     );

  //     if (memberCredit) {
  //       memberCredit.remainEL2 = member.creditEL2;
  //       memberCredit.remainEL4 = member.creditEL4;
  //     } else {
  //       memberCredit = MemberCredit.create({
  //         year: now.year,
  //         month: now.month - 1,
  //         remainEL2: member.creditEL2,
  //         remainEL4: member.creditEL4,
  //         member: {
  //           id: member.id,
  //         },
  //       });
  //     }
  //     oldMemberCredits.push(memberCredit);
  //   }

  //   await MemberCredit.save(oldMemberCredits);

  //   //ดึงข้อมูลเดือนปัจจุบันที่มีการตั้งค่าจากการ import
  //   const transactions: Transaction[] = [];
  //   const nowMemberCredits = await MemberCredit.find({
  //     where: {
  //       year: now.year,
  //       month: now.month,
  //     },
  //     relations: { member: true },
  //   });

  //   for (const member of members) {
  //     const memberCredit = nowMemberCredits.find(
  //       (m) => m.member.id == member.id,
  //     );
  //     if (memberCredit) {
  //       member.creditEL2 = memberCredit.creditEL2;
  //       member.creditEL2 = memberCredit.creditEL2;
  //     } else {
  //       member.creditEL2 = 0;
  //       member.creditEL4 = 0;
  //     }

  //     const creditList = [
  //       { type: 'EL2', credit: member.creditEL2 },
  //       { type: 'EL4', credit: member.creditEL4 },
  //     ];
  //     for (const item of creditList) {
  //       if (item.credit > 0) {
  //         const transaction = Transaction.create({
  //           date: new Date(),
  //           amount: item.credit,
  //           type: TransactionType.TOPUP,
  //           channel: TransactionChannel.SYSTEM,
  //           description: `Top up with SYSTEM amount: ${item.credit}`,
  //           member: member,
  //           walletType: item.type == 'EL2' ? WalletType.EL2 : WalletType.EL4,
  //         });
  //         transactions.push(transaction);
  //       }
  //     }
  //   }

  //   await Member.save(members);
  //   await Transaction.save(transactions);

  //   this.logger.debug('สิ้นสุดการเติมเงิน');
  // }

  // @Cron('0 5 * * *')
  // async walletRemainDaily() {
  //   const members = await Member.find({
  //     // relations: { card: true }
  //   });

  //   const logs: MemberWalletLog[] = [];
  //   for (const member of members) {
  //     const log = MemberWalletLog.create({
  //       date: new Date(),
  //       wallet: member.wallet,
  //       creditEL2: member.creditEL2,
  //       creditEL4: member.creditEL4,
  //       member: {
  //         id: member.id,
  //       },
  //     });
  //     logs.push(log);
  //   }

  //   await MemberWalletLog.save(logs);
  // }
}
