import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { PaymentMethodService } from './payment-method.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { ApiConsumes, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('payment-method')
@ApiTags('ช่องทางการชำระเงิน')
@Auth()
export class PaymentMethodController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Post()
  @ApiConsumes('application/json')
  @ApiConsumes('application/x-www-form-urlencoded')
  create(@Body() createPaymentMethodDto: CreatePaymentMethodDto) {
    return this.paymentMethodService.create(createPaymentMethodDto);
  }

  @Get()
  @ApiOperation({ summary: 'ช่องทางการชำระเงินทั้งมหด' })
  @ApiQuery({ name: 'branchId', required: false })
  findAll(@Query('branchId') branchId?: number) {
    return this.paymentMethodService.findAll(branchId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.paymentMethodService.findOne(+id);
  }

  // @Put(':id')
  // update(@Param('id') id: string, @Body() updatePaymentMethodDto: UpdatePaymentMethodDto) {
  //   return this.paymentMethodService.update(+id, updatePaymentMethodDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.paymentMethodService.remove(+id);
  // }
}
