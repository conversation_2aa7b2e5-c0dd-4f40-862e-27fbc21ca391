import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { PaymentMethod } from './entities/payment-method.entity';
import { Branch } from 'src/branch/entities/branch.entity';

@Injectable()
export class PaymentMethodService {
  constructor(
    @InjectRepository(PaymentMethod)
    private paymentMethodRepository: Repository<PaymentMethod>,
  ) {}

  async create(createPaymentMethodDto: CreatePaymentMethodDto) {
    //check payment method exist
    const exist = await this.paymentMethodRepository.exists({
      where: {
        name: createPaymentMethodDto?.name,
        // branch: {
        //   id: createPaymentMethodDto?.branchId,
        // },
      },
    });

    if (exist) {
      throw new BadRequestException('Payment method already exists');
    }

    //check branch
    const branch = await Branch.findOneBy({
      id: createPaymentMethodDto?.branchId,
    });
    if (!branch) {
      throw new NotFoundException(
        `Branch id ${createPaymentMethodDto?.branchId} not found.`,
      );
    }

    const paymentMethod = this.paymentMethodRepository.create({
      name: createPaymentMethodDto?.name,
      type: createPaymentMethodDto?.type,
      // branch: branch,
    });

    return paymentMethod.save();
  }

  async findAll(branchId: number) {
    let where: FindOptionsWhere<PaymentMethod> = {};

    if (branchId) {
      where = {
        ...where,
      };
    }

    const data = await this.paymentMethodRepository.find({ where });

    return data;
  }

  async findOne(id: number) {
    const paymentMethod = await this.paymentMethodRepository.findOneBy({ id });

    if (!paymentMethod) {
      throw new NotFoundException(`No payment method`);
    }

    return paymentMethod;
  }

  // update(id: number, updatePaymentMethodDto: UpdatePaymentMethodDto) {
  //   return `This action updates a #${id} paymentMethod`;
  // }

  // async remove(id: number) {
  //   await this.paymentMethodRepository.delete(id);
  // }
}
