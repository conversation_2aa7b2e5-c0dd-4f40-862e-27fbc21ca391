import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { PaymentMethodType } from '../entities/payment-method.entity';

export class CreatePaymentMethodDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty({ enum: ['cash', 'reserve', 'thaiqr', 'member'] })
  @IsNotEmpty()
  readonly type: PaymentMethodType;

  @IsNotEmpty()
  readonly branchId: number;
}
