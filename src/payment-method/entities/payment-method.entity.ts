import { OrderPayment } from '../../order/entities/order-payment.entity';
import { CustomBaseEntity } from '../../common/entities';
import {
  Column,
  Entity,
  OneToMany,
} from 'typeorm';

export enum PaymentMethodType {
  CASH = 'cash',
  CREDIT = 'credit',
  // RESERVE = 'reserve',
  // THAIQR = 'thaiqr',
  // MEMBER = 'member',
}

@Entity()
export class PaymentMethod extends CustomBaseEntity {
  @Column({ comment: 'Payment-method name' })
  name: string;

  @Column({ type: 'enum', enum: PaymentMethodType })
  type: PaymentMethodType;

  @OneToMany(() => OrderPayment, (op: OrderPayment) => op.paymentMethod)
  orderPayments: OrderPayment[];
}
