import { IsNotEmpty, IsStrongPassword } from 'class-validator';

export class SignUpDto {

  @IsNotEmpty({ message: 'Username is required' })
  readonly username: string;

  @IsNotEmpty({ message: 'Password is required' })
  @IsStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minSymbols: 1,
    minNumbers: 1,
  })
  readonly password: string;

  @IsNotEmpty({ message: 'Email is required' })
  readonly email: string;

  @IsNotEmpty({ message: 'Firstname is required' })
  readonly firstName: string;

  @IsNotEmpty({ message: 'Lastname is required' })
  readonly lastName: string;

}
