import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { User } from '../../user/entities/user.entity';

export type JwtPayload = {
  sub: number;
  username: string;
  storeId: number;
  permissions: string[];
};

@Injectable()
export class AccessTokenStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: process.env.JWT_ACCESS_SECRET,
    });
  }

  async validate(payload: JwtPayload) {
    const user = await User.findOne({
      where: { id: payload.sub },
      relations: { store: true, roles: { permissions: true } },
    });

    const permissions = user.roles.map((role) => role.permissions.map((permission) => permission.name)).flat();

    payload.storeId = user?.store?.id;
    payload.permissions = permissions;

    return payload;
  }
}
