import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateShiftDto {
  @ApiProperty()
  @IsNotEmpty({ message: 'Device is required' })
  readonly deviceId: number;

  @ApiProperty()
  @IsNotEmpty({ message: 'change is required' })
  readonly change: number;

  @ApiProperty()
  @IsNotEmpty({ message: 'Cash is required' })
  readonly cash: number;

  @ApiProperty({ required: false })
  readonly remark?: string;
}
