import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Req,
  Put,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { SHIFT_PAGINATION_CONFIG, ShiftService } from './shift.service';
import { CreateShiftDto } from './dto/create-shift.dto';
import { UpdateShiftDto } from './dto/update-shift.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('shift')
@ApiTags('กะทำงาน')
@Auth()
export class ShiftController {
  constructor(private readonly shiftService: ShiftService) { }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(SHIFT_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.shiftService.datatables(query);
  }

  @Post()
  @ApiOperation({ summary: 'เปิดกะ' })
  async create(@Req() req: Request, @Body() createShiftDto: CreateShiftDto) {
    const user = req.user;

    const currentShift = await this.shiftService.findCurrentShift(user, createShiftDto.deviceId);
    if (currentShift) {
      throw new BadRequestException('Shift already open');
    }

    return this.shiftService.create(user, createShiftDto);
  }

  @Post(':id/off')
  shiftOff(@Req() req: Request, @Param('id') id: string) {
    const userId = req.user['sub'];

    return this.shiftService.shiftOff(+id);
  }

  @Get()
  findAll() {
    return this.shiftService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.shiftService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateShiftDto: UpdateShiftDto) {
    return this.shiftService.update(+id, updateShiftDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.shiftService.remove(+id);
  }
}
