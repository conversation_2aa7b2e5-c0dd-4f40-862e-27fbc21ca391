import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsEnum, IsDateString, Min } from 'class-validator';
import { InventoryTransactionType, InventoryTransactionStatus } from '../entities/inventory-transaction.entity';

export class CreateInventoryTransactionDto {
  @ApiProperty({ description: 'รหัสสินค้าคงคลัง' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้าคงคลัง' })
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังต้องเป็นตัวเลข' })
  readonly inventoryId: number;

  @ApiProperty({
    description: 'ประเภทของธุรกรรมสินค้าคงคลัง',
    enum: InventoryTransactionType
  })
  @IsNotEmpty({ message: 'กรุณาระบุประเภทธุรกรรม' })
  @IsEnum(InventoryTransactionType, { message: 'ประเภทธุรกรรมไม่ถูกต้อง' })
  readonly type: InventoryTransactionType;

  @ApiProperty({
    description: 'สถานะของธุรกรรม',
    enum: InventoryTransactionStatus,
    default: InventoryTransactionStatus.COMPLETED
  })
  @IsOptional()
  @IsEnum(InventoryTransactionStatus, { message: 'สถานะธุรกรรมไม่ถูกต้อง' })
  readonly status?: InventoryTransactionStatus = InventoryTransactionStatus.COMPLETED;

  @ApiProperty({ description: 'จำนวนที่เกี่ยวข้องในธุรกรรม' })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวน' })
  @IsNumber({}, { message: 'จำนวนต้องเป็นตัวเลข' })
  readonly quantity: number;

  @ApiProperty({ description: 'ต้นทุนต่อหน่วยในขณะที่ทำธุรกรรม', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ต้นทุนต่อหน่วยต้องเป็นตัวเลข' })
  @Min(0, { message: 'ต้นทุนต่อหน่วยต้องไม่ติดลบ' })
  readonly unitCost?: number;

  @ApiProperty({ description: 'หมายเลขอ้างอิงเอกสารภายนอก', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขอ้างอิงต้องเป็นข้อความ' })
  readonly referenceNo?: string;

  @ApiProperty({ description: 'หมายเหตุเพิ่มเติมเกี่ยวกับธุรกรรม', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ description: 'เหตุผลของการทำธุรกรรม', required: false })
  @IsOptional()
  @IsString({ message: 'เหตุผลต้องเป็นข้อความ' })
  readonly reason?: string;

  @ApiProperty({ description: 'ข้อมูลผู้จำหน่ายหรือผู้ขาย', required: false })
  @IsOptional()
  @IsString({ message: 'ข้อมูลผู้จำหน่ายต้องเป็นข้อความ' })
  readonly supplier?: string;

  @ApiProperty({ description: 'หมายเลขแบทช์หรือล็อต', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขแบทช์ต้องเป็นข้อความ' })
  readonly batchNo?: string;

  @ApiProperty({ description: 'วันหมดอายุสำหรับสินค้าที่เสื่อมเสีย', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'วันหมดอายุต้องเป็นรูปแบบวันที่ที่ถูกต้อง' })
  readonly expiryDate?: string;
}
