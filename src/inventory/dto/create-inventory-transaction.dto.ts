import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsEnum, IsDateString, Min } from 'class-validator';
import { InventoryTransactionType, InventoryTransactionStatus } from '../entities/inventory-transaction.entity';

export class CreateInventoryTransactionDto {
  @ApiProperty({ description: 'Inventory ID' })
  @IsNotEmpty({ message: 'Inventory ID is required' })
  @IsNumber({}, { message: 'Inventory ID must be a number' })
  readonly inventoryId: number;

  @ApiProperty({ 
    description: 'Type of inventory transaction',
    enum: InventoryTransactionType 
  })
  @IsNotEmpty({ message: 'Transaction type is required' })
  @IsEnum(InventoryTransactionType, { message: 'Invalid transaction type' })
  readonly type: InventoryTransactionType;

  @ApiProperty({ 
    description: 'Status of the transaction',
    enum: InventoryTransactionStatus,
    default: InventoryTransactionStatus.COMPLETED 
  })
  @IsOptional()
  @IsEnum(InventoryTransactionStatus, { message: 'Invalid transaction status' })
  readonly status?: InventoryTransactionStatus = InventoryTransactionStatus.COMPLETED;

  @ApiProperty({ description: 'Quantity involved in the transaction' })
  @IsNotEmpty({ message: 'Quantity is required' })
  @IsNumber({}, { message: 'Quantity must be a number' })
  readonly quantity: number;

  @ApiProperty({ description: 'Unit cost at the time of transaction', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'Unit cost must be a number' })
  @Min(0, { message: 'Unit cost cannot be negative' })
  readonly unitCost?: number;

  @ApiProperty({ description: 'Reference to external document', required: false })
  @IsOptional()
  @IsString({ message: 'Reference number must be a string' })
  readonly referenceNo?: string;

  @ApiProperty({ description: 'Additional notes about the transaction', required: false })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  readonly notes?: string;

  @ApiProperty({ description: 'Reason for the transaction', required: false })
  @IsOptional()
  @IsString({ message: 'Reason must be a string' })
  readonly reason?: string;

  @ApiProperty({ description: 'Supplier or vendor information', required: false })
  @IsOptional()
  @IsString({ message: 'Supplier must be a string' })
  readonly supplier?: string;

  @ApiProperty({ description: 'Batch or lot number', required: false })
  @IsOptional()
  @IsString({ message: 'Batch number must be a string' })
  readonly batchNo?: string;

  @ApiProperty({ description: 'Expiry date for perishable items', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'Expiry date must be a valid date' })
  readonly expiryDate?: string;
}
