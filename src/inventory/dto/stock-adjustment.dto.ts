import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class StockAdjustmentDto {
  @ApiProperty({ description: 'Inventory ID' })
  @IsNotEmpty({ message: 'Inventory ID is required' })
  @IsNumber({}, { message: 'Inventory ID must be a number' })
  readonly inventoryId: number;

  @ApiProperty({ description: 'New stock quantity' })
  @IsNotEmpty({ message: 'New quantity is required' })
  @IsNumber({}, { message: 'New quantity must be a number' })
  readonly newQuantity: number;

  @ApiProperty({ description: 'Reason for adjustment', required: false })
  @IsOptional()
  @IsString({ message: 'Reason must be a string' })
  readonly reason?: string;

  @ApiProperty({ description: 'Additional notes', required: false })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  readonly notes?: string;

  @ApiProperty({ description: 'Reference number', required: false })
  @IsOptional()
  @IsString({ message: 'Reference number must be a string' })
  readonly referenceNo?: string;
}

export class StockTransferDto {
  @ApiProperty({ description: 'Source inventory ID' })
  @IsNotEmpty({ message: 'Source inventory ID is required' })
  @IsNumber({}, { message: 'Source inventory ID must be a number' })
  readonly fromInventoryId: number;

  @ApiProperty({ description: 'Destination inventory ID' })
  @IsNotEmpty({ message: 'Destination inventory ID is required' })
  @IsNumber({}, { message: 'Destination inventory ID must be a number' })
  readonly toInventoryId: number;

  @ApiProperty({ description: 'Quantity to transfer' })
  @IsNotEmpty({ message: 'Quantity is required' })
  @IsNumber({}, { message: 'Quantity must be a number' })
  readonly quantity: number;

  @ApiProperty({ description: 'Transfer notes', required: false })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  readonly notes?: string;

  @ApiProperty({ description: 'Reference number', required: false })
  @IsOptional()
  @IsString({ message: 'Reference number must be a string' })
  readonly referenceNo?: string;
}
