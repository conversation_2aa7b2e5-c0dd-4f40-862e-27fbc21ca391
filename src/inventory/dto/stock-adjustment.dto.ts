import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class StockAdjustmentDto {
  @ApiProperty({ description: 'รหัสสินค้าคงคลัง' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้าคงคลัง' })
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังต้องเป็นตัวเลข' })
  readonly inventoryId: number;

  @ApiProperty({ description: 'จำนวนสต็อกใหม่' })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวนใหม่' })
  @IsNumber({}, { message: 'จำนวนใหม่ต้องเป็นตัวเลข' })
  readonly newQuantity: number;

  @ApiProperty({ description: 'เหตุผลในการปรับปรุง', required: false })
  @IsOptional()
  @IsString({ message: 'เหตุผลต้องเป็นข้อความ' })
  readonly reason?: string;

  @ApiProperty({ description: 'หมายเหตุเพิ่มเติม', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ description: 'หมายเลขอ้างอิง', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขอ้างอิงต้องเป็นข้อความ' })
  readonly referenceNo?: string;
}

export class StockTransferDto {
  @ApiProperty({ description: 'รหัสสินค้าคงคลังต้นทาง' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้าคงคลังต้นทาง' })
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังต้นทางต้องเป็นตัวเลข' })
  readonly fromInventoryId: number;

  @ApiProperty({ description: 'รหัสสินค้าคงคลังปลายทาง' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้าคงคลังปลายทาง' })
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังปลายทางต้องเป็นตัวเลข' })
  readonly toInventoryId: number;

  @ApiProperty({ description: 'จำนวนที่ต้องการโอน' })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวน' })
  @IsNumber({}, { message: 'จำนวนต้องเป็นตัวเลข' })
  readonly quantity: number;

  @ApiProperty({ description: 'หมายเหตุการโอน', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ description: 'หมายเลขอ้างอิง', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขอ้างอิงต้องเป็นข้อความ' })
  readonly referenceNo?: string;
}
