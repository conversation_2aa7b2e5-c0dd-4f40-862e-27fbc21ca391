import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsBoolean, IsString, Min } from 'class-validator';

export class CreateInventoryDto {
  @ApiProperty({ description: 'รหัสสินค้า' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้า' })
  @IsNumber({}, { message: 'รหัสสินค้าต้องเป็นตัวเลข' })
  readonly productId: number;

  @ApiProperty({ description: 'รหัสสาขา' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสาขา' })
  @IsNumber({}, { message: 'รหัสสาขาต้องเป็นตัวเลข' })
  readonly branchId: number;

  @ApiProperty({ description: 'จำนวนสต็อกเริ่มต้น', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'จำนวนสต็อกปัจจุบันต้องเป็นตัวเลข' })
  @Min(0, { message: 'จำนวนสต็อกปัจจุบันต้องไม่ติดลบ' })
  readonly currentStock?: number = 0;

  @ApiProperty({ description: 'จำนวนสต็อกขั้นต่ำสำหรับการแจ้งเตือน', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'จำนวนสต็อกขั้นต่ำต้องเป็นตัวเลข' })
  @Min(0, { message: 'จำนวนสต็อกขั้นต่ำต้องไม่ติดลบ' })
  readonly minStock?: number = 0;

  @ApiProperty({ description: 'จำนวนสต็อกสูงสุด', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'จำนวนสต็อกสูงสุดต้องเป็นตัวเลข' })
  @Min(0, { message: 'จำนวนสต็อกสูงสุดต้องไม่ติดลบ' })
  readonly maxStock?: number;

  @ApiProperty({ description: 'จำนวนสต็อกที่จองไว้', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'จำนวนสต็อกที่จองไว้ต้องเป็นตัวเลข' })
  @Min(0, { message: 'จำนวนสต็อกที่จองไว้ต้องไม่ติดลบ' })
  readonly reservedStock?: number = 0;

  @ApiProperty({ description: 'ต้นทุนเฉลี่ยของสินค้าคงคลัง', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ต้นทุนเฉลี่ยต้องเป็นตัวเลข' })
  @Min(0, { message: 'ต้นทุนเฉลี่ยต้องไม่ติดลบ' })
  readonly averageCost?: number;

  @ApiProperty({ description: 'ต้นทุนการซื้อครั้งล่าสุด', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ต้นทุนล่าสุดต้องเป็นตัวเลข' })
  @Min(0, { message: 'ต้นทุนล่าสุดต้องไม่ติดลบ' })
  readonly lastCost?: number;

  @ApiProperty({ description: 'สถานะการใช้งานการติดตามสินค้าคงคลัง', default: true })
  @IsOptional()
  @IsBoolean({ message: 'สถานะการใช้งานต้องเป็น boolean' })
  readonly active?: boolean = true;

  @ApiProperty({ description: 'ตำแหน่งที่เก็บภายในสาขา', required: false })
  @IsOptional()
  @IsString({ message: 'ตำแหน่งที่เก็บต้องเป็นข้อความ' })
  readonly location?: string;

  @ApiProperty({ description: 'หมายเหตุเพิ่มเติมเกี่ยวกับสินค้าคงคลัง', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;
}
