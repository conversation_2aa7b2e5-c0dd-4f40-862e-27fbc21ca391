import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsBoolean, IsString, Min } from 'class-validator';

export class CreateInventoryDto {
  @ApiProperty({ description: 'Product ID' })
  @IsNotEmpty({ message: 'Product ID is required' })
  @IsNumber({}, { message: 'Product ID must be a number' })
  readonly productId: number;

  @ApiProperty({ description: 'Branch ID' })
  @IsNotEmpty({ message: 'Branch ID is required' })
  @IsNumber({}, { message: 'Branch ID must be a number' })
  readonly branchId: number;

  @ApiProperty({ description: 'Initial stock quantity', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'Current stock must be a number' })
  @Min(0, { message: 'Current stock cannot be negative' })
  readonly currentStock?: number = 0;

  @ApiProperty({ description: 'Minimum stock level for alerts', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'Minimum stock must be a number' })
  @Min(0, { message: 'Minimum stock cannot be negative' })
  readonly minStock?: number = 0;

  @ApiProperty({ description: 'Maximum stock level', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'Maximum stock must be a number' })
  @Min(0, { message: 'Maximum stock cannot be negative' })
  readonly maxStock?: number;

  @ApiProperty({ description: 'Reserved stock quantity', default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'Reserved stock must be a number' })
  @Min(0, { message: 'Reserved stock cannot be negative' })
  readonly reservedStock?: number = 0;

  @ApiProperty({ description: 'Average cost of the inventory', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'Average cost must be a number' })
  @Min(0, { message: 'Average cost cannot be negative' })
  readonly averageCost?: number;

  @ApiProperty({ description: 'Last purchase cost', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'Last cost must be a number' })
  @Min(0, { message: 'Last cost cannot be negative' })
  readonly lastCost?: number;

  @ApiProperty({ description: 'Whether inventory tracking is active', default: true })
  @IsOptional()
  @IsBoolean({ message: 'Active must be a boolean' })
  readonly active?: boolean = true;

  @ApiProperty({ description: 'Location within the branch', required: false })
  @IsOptional()
  @IsString({ message: 'Location must be a string' })
  readonly location?: string;

  @ApiProperty({ description: 'Additional notes about the inventory', required: false })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  readonly notes?: string;
}
