import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Inventory } from './inventory.entity';
import { User } from '../../user/entities/user.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

export enum InventoryTransactionType {
  STOCK_IN = 'stock_in', // รับสินค้าเข้า
  STOCK_OUT = 'stock_out', // เบิกสินค้าออก
  ADJUSTMENT = 'adjustment', // ปรับปรุงสต็อก
  TRANSFER = 'transfer', // โอนย้ายสินค้า
  SALE = 'sale', // ขายสินค้า
  RETURN = 'return', // คืนสินค้า
  DAMAGE = 'damage', // สินค้าเสียหาย
  EXPIRED = 'expired', // สินค้าหมดอายุ
  INITIAL = 'initial' // สต็อกเริ่มต้น
}

export enum InventoryTransactionStatus {
  PENDING = 'pending', // รอดำเนินการ
  COMPLETED = 'completed', // เสร็จสิ้น
  CANCELLED = 'cancelled' // ยกเลิก
}

@Entity()
export class InventoryTransaction extends CustomBaseEntity {
  @Column({
    comment: 'หมายเลขอ้างอิงธุรกรรมที่ไม่ซ้ำ'
  })
  @Index()
  transactionNo: string;

  @Column({
    type: 'enum',
    enum: InventoryTransactionType,
    comment: 'ประเภทของธุรกรรมสินค้าคงคลัง'
  })
  type: InventoryTransactionType;

  @Column({
    type: 'enum',
    enum: InventoryTransactionStatus,
    default: InventoryTransactionStatus.COMPLETED,
    comment: 'สถานะของธุรกรรม'
  })
  status: InventoryTransactionStatus;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนที่เกี่ยวข้องในธุรกรรม'
  })
  quantity: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนต่อหน่วยในขณะที่ทำธุรกรรม'
  })
  unitCost: number;

  @Column({ 
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนรวมของธุรกรรม'
  })
  totalCost: number;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกก่อนทำธุรกรรม'
  })
  stockBefore: number;

  @Column({
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสต็อกหลังทำธุรกรรม'
  })
  stockAfter: number;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'วันที่และเวลาของธุรกรรม'
  })
  transactionDate: Date;

  @Column({
    nullable: true,
    comment: 'หมายเลขอ้างอิงเอกสารภายนอก (ใบสั่งซื้อ, ใบแจ้งหนี้, ฯลฯ)'
  })
  referenceNo: string;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมเกี่ยวกับธุรกรรม'
  })
  notes: string;

  @Column({
    nullable: true,
    comment: 'เหตุผลของการทำธุรกรรม (โดยเฉพาะสำหรับการปรับปรุง)'
  })
  reason: string;

  @Column({
    nullable: true,
    comment: 'ข้อมูลผู้จำหน่ายหรือผู้ขายสำหรับธุรกรรมรับสินค้าเข้า'
  })
  supplier: string;

  @Column({
    nullable: true,
    comment: 'หมายเลขแบทช์หรือล็อตสำหรับการติดตาม'
  })
  batchNo: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'วันหมดอายุสำหรับสินค้าที่เสื่อมเสีย'
  })
  expiryDate: Date;

  // ความสัมพันธ์
  @ManyToOne(() => Inventory, (inventory) => inventory.transactions)
  @JoinColumn({ name: 'inventory_id' })
  inventory: Inventory;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approvedBy: User;

  constructor(partial?: Partial<InventoryTransaction>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
