import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Inventory } from './inventory.entity';
import { User } from '../../user/entities/user.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

export enum InventoryTransactionType {
  STOCK_IN = 'stock_in',
  STOCK_OUT = 'stock_out',
  ADJUSTMENT = 'adjustment',
  TRANSFER = 'transfer',
  SALE = 'sale',
  RETURN = 'return',
  DAMAGE = 'damage',
  EXPIRED = 'expired',
  INITIAL = 'initial'
}

export enum InventoryTransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

@Entity()
export class InventoryTransaction extends CustomBaseEntity {
  @Column({ 
    comment: 'Unique transaction reference number' 
  })
  @Index()
  transactionNo: string;

  @Column({ 
    type: 'enum', 
    enum: InventoryTransactionType,
    comment: 'Type of inventory transaction' 
  })
  type: InventoryTransactionType;

  @Column({ 
    type: 'enum', 
    enum: InventoryTransactionStatus,
    default: InventoryTransactionStatus.COMPLETED,
    comment: 'Status of the transaction' 
  })
  status: InventoryTransactionStatus;

  @Column({ 
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'Quantity involved in the transaction' 
  })
  quantity: number;

  @Column({ 
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Unit cost at the time of transaction' 
  })
  unitCost: number;

  @Column({ 
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Total cost of the transaction' 
  })
  totalCost: number;

  @Column({ 
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'Stock quantity before the transaction' 
  })
  stockBefore: number;

  @Column({ 
    type: 'numeric',
    transformer: new DecimalColumnTransformer(),
    comment: 'Stock quantity after the transaction' 
  })
  stockAfter: number;

  @Column({ 
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'Date and time of the transaction' 
  })
  transactionDate: Date;

  @Column({ 
    nullable: true,
    comment: 'Reference to external document (PO, Invoice, etc.)' 
  })
  referenceNo: string;

  @Column({ 
    nullable: true,
    comment: 'Additional notes about the transaction' 
  })
  notes: string;

  @Column({ 
    nullable: true,
    comment: 'Reason for the transaction (especially for adjustments)' 
  })
  reason: string;

  @Column({ 
    nullable: true,
    comment: 'Supplier or vendor information for stock in transactions' 
  })
  supplier: string;

  @Column({ 
    nullable: true,
    comment: 'Batch or lot number for tracking' 
  })
  batchNo: string;

  @Column({ 
    type: 'date',
    nullable: true,
    comment: 'Expiry date for perishable items' 
  })
  expiryDate: Date;

  // Relationships
  @ManyToOne(() => Inventory, (inventory) => inventory.transactions)
  @JoinColumn({ name: 'inventory_id' })
  inventory: Inventory;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approvedBy: User;

  constructor(partial?: Partial<InventoryTransaction>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
