import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Product } from '../../product/entities/product.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Store } from '../../store/entities/store.entity';
import { InventoryTransaction } from './inventory-transaction.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

@Entity()
@Unique(['product', 'branch'])
export class Inventory extends CustomBaseEntity {
  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าคงคลังปัจจุบัน'
  })
  currentStock: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าขั้นต่ำสำหรับการแจ้งเตือน'
  })
  minStock: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าสูงสุด'
  })
  maxStock: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าที่จองไว้'
  })
  reservedStock: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'จำนวนสินค้าที่พร้อมใช้ (ปัจจุบัน - จอง)'
  })
  availableStock: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนเฉลี่ยของสินค้าคงคลัง'
  })
  averageCost: number;

  @Column({
    type: 'numeric',
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'ต้นทุนการซื้อครั้งล่าสุด'
  })
  lastCost: number;

  @Column({
    default: true,
    comment: 'สถานะการใช้งานการติดตามสินค้าคงคลังสำหรับสินค้า-สาขานี้'
  })
  active: boolean;

  @Column({
    nullable: true,
    comment: 'ตำแหน่งที่เก็บภายในสาขา (ชั้น, ทางเดิน, ฯลฯ)'
  })
  location: string;

  @Column({
    nullable: true,
    comment: 'หมายเหตุเพิ่มเติมเกี่ยวกับสินค้าคงคลัง'
  })
  notes: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'วันเวลาที่อัปเดตสต็อกล่าสุด'
  })
  lastUpdated: Date;

  // ความสัมพันธ์
  @ManyToOne(() => Product, (product) => product.inventories)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Branch, (branch) => branch.inventories)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @ManyToOne(() => Store, (store) => store.inventories)
  @JoinColumn({ name: 'store_id' })
  store: Store;

  @OneToMany(() => InventoryTransaction, (transaction) => transaction.inventory)
  transactions: InventoryTransaction[];

  // คุณสมบัติที่คำนวณได้
  get isLowStock(): boolean {
    return this.currentStock <= this.minStock;
  }

  get stockValue(): number {
    return this.currentStock * (this.averageCost || 0);
  }

  get stockStatus(): 'low' | 'normal' | 'high' | 'out' {
    if (this.currentStock <= 0) return 'out'; // หมดสต็อก
    if (this.currentStock <= this.minStock) return 'low'; // สต็อกต่ำ
    if (this.maxStock && this.currentStock >= this.maxStock) return 'high'; // สต็อกสูง
    return 'normal'; // ปกติ
  }

  constructor(partial?: Partial<Inventory>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
