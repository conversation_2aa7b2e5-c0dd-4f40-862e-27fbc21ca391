import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { Product } from '../../product/entities/product.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Store } from '../../store/entities/store.entity';
import { InventoryTransaction } from './inventory-transaction.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';

@Entity()
@Unique(['product', 'branch'])
export class Inventory extends CustomBaseEntity {
  @Column({ 
    type: 'numeric', 
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Current stock quantity' 
  })
  currentStock: number;

  @Column({ 
    type: 'numeric', 
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Minimum stock level for alerts' 
  })
  minStock: number;

  @Column({ 
    type: 'numeric', 
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Maximum stock level' 
  })
  maxStock: number;

  @Column({ 
    type: 'numeric', 
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Reserved stock quantity' 
  })
  reservedStock: number;

  @Column({ 
    type: 'numeric', 
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Available stock quantity (current - reserved)' 
  })
  availableStock: number;

  @Column({ 
    type: 'numeric', 
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Average cost of the inventory' 
  })
  averageCost: number;

  @Column({ 
    type: 'numeric', 
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Last purchase cost' 
  })
  lastCost: number;

  @Column({ 
    default: true,
    comment: 'Whether inventory tracking is active for this product-branch combination' 
  })
  active: boolean;

  @Column({ 
    nullable: true,
    comment: 'Location within the branch (shelf, aisle, etc.)' 
  })
  location: string;

  @Column({ 
    nullable: true,
    comment: 'Additional notes about the inventory' 
  })
  notes: string;

  @Column({ 
    type: 'timestamp',
    nullable: true,
    comment: 'Last time stock was updated' 
  })
  lastUpdated: Date;

  // Relationships
  @ManyToOne(() => Product, (product) => product.inventories)
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Branch, (branch) => branch.inventories)
  @JoinColumn({ name: 'branch_id' })
  branch: Branch;

  @ManyToOne(() => Store, (store) => store.inventories)
  @JoinColumn({ name: 'store_id' })
  store: Store;

  @OneToMany(() => InventoryTransaction, (transaction) => transaction.inventory)
  transactions: InventoryTransaction[];

  // Computed properties
  get isLowStock(): boolean {
    return this.currentStock <= this.minStock;
  }

  get stockValue(): number {
    return this.currentStock * (this.averageCost || 0);
  }

  get stockStatus(): 'low' | 'normal' | 'high' | 'out' {
    if (this.currentStock <= 0) return 'out';
    if (this.currentStock <= this.minStock) return 'low';
    if (this.maxStock && this.currentStock >= this.maxStock) return 'high';
    return 'normal';
  }

  constructor(partial?: Partial<Inventory>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
