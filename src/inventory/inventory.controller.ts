import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Put,
  Query,
  ParseIntPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Request } from 'express';

import {
  InventoryService,
  INVENTORY_PAGINATION_CONFIG,
  INVENTORY_TRANSACTION_PAGINATION_CONFIG,
} from './inventory.service';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { CreateInventoryTransactionDto } from './dto/create-inventory-transaction.dto';
import { StockAdjustmentDto, StockTransferDto } from './dto/stock-adjustment.dto';
import { Auth } from '../auth/decorators/auth.decorator';

@Controller('inventory')
@ApiTags('สินค้าคงคลัง (Inventory)')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(INVENTORY_PAGINATION_CONFIG)
  @ApiOperation({ summary: 'ดึงรายการสินค้าคงคลังแบบแบ่งหน้า' })
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;
    return this.inventoryService.datatables(query, user);
  }

  @Get('transactions/datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(INVENTORY_TRANSACTION_PAGINATION_CONFIG)
  @ApiOperation({ summary: 'ดึงรายการธุรกรรมสินค้าคงคลังแบบแบ่งหน้า' })
  transactionDatatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;
    return this.inventoryService.transactionDatatables(query, user);
  }

  @Post()
  @ApiOperation({ summary: 'สร้างข้อมูลสินค้าคงคลังใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างสินค้าคงคลังสำเร็จ' })
  @ApiResponse({ status: 409, description: 'มีสินค้าคงคลังสำหรับสินค้า-สาขานี้อยู่แล้ว' })
  create(@Req() req: Request, @Body() createInventoryDto: CreateInventoryDto) {
    const user = req.user;
    return this.inventoryService.create(createInventoryDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลสินค้าคงคลังทั้งหมด' })
  @ApiQuery({ name: 'productId', required: false, type: Number })
  @ApiQuery({ name: 'branchId', required: false, type: Number })
  @ApiQuery({ name: 'lowStock', required: false, type: Boolean })
  findAll(
    @Req() req: Request,
    @Query('productId') productId?: number,
    @Query('branchId') branchId?: number,
    @Query('lowStock') lowStock?: boolean,
  ) {
    const user = req.user;
    const filters = { productId, branchId, lowStock };
    return this.inventoryService.findAll(user, filters);
  }

  @Get('low-stock')
  @ApiOperation({ summary: 'ดึงรายการสินค้าที่มีสต็อกต่ำ' })
  @ApiResponse({ status: 200, description: 'ดึงรายการสินค้าสต็อกต่ำสำเร็จ' })
  getLowStockItems(@Req() req: Request) {
    const user = req.user;
    return this.inventoryService.getLowStockItems(user);
  }

  @Get('report')
  @ApiOperation({ summary: 'ดึงรายงานสินค้าคงคลัง' })
  @ApiQuery({ name: 'branchId', required: false, type: Number })
  @ApiQuery({ name: 'categoryId', required: false, type: Number })
  getInventoryReport(
    @Req() req: Request,
    @Query('branchId') branchId?: number,
    @Query('categoryId') categoryId?: number,
  ) {
    const user = req.user;
    const filters = { branchId, categoryId };
    return this.inventoryService.getInventoryReport(user, filters);
  }

  @Get('product/:productId/branch/:branchId')
  @ApiOperation({ summary: 'ดึงข้อมูลสินค้าคงคลังตามสินค้าและสาขา' })
  @ApiResponse({ status: 200, description: 'พบสินค้าคงคลัง' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  findByProductAndBranch(
    @Req() req: Request,
    @Param('productId', ParseIntPipe) productId: number,
    @Param('branchId', ParseIntPipe) branchId: number,
  ) {
    const user = req.user;
    return this.inventoryService.findByProductAndBranch(productId, branchId, user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลสินค้าคงคลังตาม ID' })
  @ApiResponse({ status: 200, description: 'พบสินค้าคงคลัง' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.inventoryService.findOne(id);
  }

  @Get(':id/transactions')
  @ApiOperation({ summary: 'ดึงประวัติธุรกรรมของสินค้าคงคลัง' })
  getTransactionHistory(@Req() req: Request, @Param('id', ParseIntPipe) id: number) {
    const user = req.user;
    return this.inventoryService.getTransactionHistory(id, user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตข้อมูลสินค้าคงคลัง' })
  @ApiResponse({ status: 200, description: 'อัปเดตสินค้าคงคลังสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ) {
    return this.inventoryService.update(id, updateInventoryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบข้อมูลสินค้าคงคลัง' })
  @ApiResponse({ status: 200, description: 'ลบสินค้าคงคลังสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.inventoryService.remove(id);
  }

  @Post('stock-in')
  @ApiOperation({ summary: 'เพิ่มสต็อกเข้าสินค้าคงคลัง' })
  @ApiResponse({ status: 200, description: 'เพิ่มสต็อกสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  stockIn(@Req() req: Request, @Body() createTransactionDto: CreateInventoryTransactionDto) {
    const user = req.user;
    return this.inventoryService.stockIn(createTransactionDto, user);
  }

  @Post('stock-out')
  @ApiOperation({ summary: 'เบิกสต็อกออกจากสินค้าคงคลัง' })
  @ApiResponse({ status: 200, description: 'เบิกสต็อกสำเร็จ' })
  @ApiResponse({ status: 400, description: 'สต็อกไม่เพียงพอ' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  stockOut(@Req() req: Request, @Body() createTransactionDto: CreateInventoryTransactionDto) {
    const user = req.user;
    return this.inventoryService.stockOut(createTransactionDto, user);
  }

  @Post('adjust')
  @ApiOperation({ summary: 'ปรับปรุงสต็อกสินค้าคงคลัง' })
  @ApiResponse({ status: 200, description: 'ปรับปรุงสต็อกสำเร็จ' })
  @ApiResponse({ status: 400, description: 'การปรับปรุงไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  adjustStock(@Req() req: Request, @Body() stockAdjustmentDto: StockAdjustmentDto) {
    const user = req.user;
    return this.inventoryService.adjustStock(stockAdjustmentDto, user);
  }

  @Post('transfer')
  @ApiOperation({ summary: 'โอนสต็อกระหว่างสาขา' })
  @ApiResponse({ status: 200, description: 'โอนสต็อกสำเร็จ' })
  @ApiResponse({ status: 400, description: 'สต็อกไม่เพียงพอสำหรับการโอน' })
  @ApiResponse({ status: 404, description: 'ไม่พบสินค้าคงคลัง' })
  transferStock(@Req() req: Request, @Body() stockTransferDto: StockTransferDto) {
    const user = req.user;
    return this.inventoryService.transferStock(stockTransferDto, user);
  }

  @Post(':id/reserve')
  @ApiOperation({ summary: 'จองสต็อก' })
  @ApiResponse({ status: 200, description: 'จองสต็อกสำเร็จ' })
  @ApiResponse({ status: 400, description: 'สต็อกที่พร้อมใช้ไม่เพียงพอ' })
  reserveStock(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('quantity') quantity: number,
  ) {
    const user = req.user;
    return this.inventoryService.reserveStock(id, quantity, user);
  }

  @Post(':id/release')
  @ApiOperation({ summary: 'ปลดปล่อยสต็อกที่จองไว้' })
  @ApiResponse({ status: 200, description: 'ปลดปล่อยสต็อกที่จองไว้สำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถปลดปล่อยมากกว่าที่จองไว้ได้' })
  releaseReservedStock(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('quantity') quantity: number,
  ) {
    const user = req.user;
    return this.inventoryService.releaseReservedStock(id, quantity, user);
  }
}
