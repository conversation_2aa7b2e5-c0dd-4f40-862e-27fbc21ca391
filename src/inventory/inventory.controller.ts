import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Put,
  Query,
  ParseIntPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Request } from 'express';

import {
  InventoryService,
  INVENTORY_PAGINATION_CONFIG,
  INVENTORY_TRANSACTION_PAGINATION_CONFIG,
} from './inventory.service';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { CreateInventoryTransactionDto } from './dto/create-inventory-transaction.dto';
import { StockAdjustmentDto, StockTransferDto } from './dto/stock-adjustment.dto';
import { Auth } from '../auth/decorators/auth.decorator';

@Controller('inventory')
@ApiTags('สินค้าคงคลัง')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(INVENTORY_PAGINATION_CONFIG)
  @ApiOperation({ summary: 'Get paginated inventory list' })
  datatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;
    return this.inventoryService.datatables(query, user);
  }

  @Get('transactions/datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(INVENTORY_TRANSACTION_PAGINATION_CONFIG)
  @ApiOperation({ summary: 'Get paginated inventory transactions list' })
  transactionDatatables(@Req() req: Request, @Paginate() query: PaginateQuery) {
    const user = req.user;
    return this.inventoryService.transactionDatatables(query, user);
  }

  @Post()
  @ApiOperation({ summary: 'Create new inventory record' })
  @ApiResponse({ status: 201, description: 'Inventory created successfully' })
  @ApiResponse({ status: 409, description: 'Inventory already exists for this product-branch combination' })
  create(@Req() req: Request, @Body() createInventoryDto: CreateInventoryDto) {
    const user = req.user;
    return this.inventoryService.create(createInventoryDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all inventory records' })
  @ApiQuery({ name: 'productId', required: false, type: Number })
  @ApiQuery({ name: 'branchId', required: false, type: Number })
  @ApiQuery({ name: 'lowStock', required: false, type: Boolean })
  findAll(
    @Req() req: Request,
    @Query('productId') productId?: number,
    @Query('branchId') branchId?: number,
    @Query('lowStock') lowStock?: boolean,
  ) {
    const user = req.user;
    const filters = { productId, branchId, lowStock };
    return this.inventoryService.findAll(user, filters);
  }

  @Get('low-stock')
  @ApiOperation({ summary: 'Get items with low stock' })
  @ApiResponse({ status: 200, description: 'Low stock items retrieved successfully' })
  getLowStockItems(@Req() req: Request) {
    const user = req.user;
    return this.inventoryService.getLowStockItems(user);
  }

  @Get('report')
  @ApiOperation({ summary: 'Get inventory report' })
  @ApiQuery({ name: 'branchId', required: false, type: Number })
  @ApiQuery({ name: 'categoryId', required: false, type: Number })
  getInventoryReport(
    @Req() req: Request,
    @Query('branchId') branchId?: number,
    @Query('categoryId') categoryId?: number,
  ) {
    const user = req.user;
    const filters = { branchId, categoryId };
    return this.inventoryService.getInventoryReport(user, filters);
  }

  @Get('product/:productId/branch/:branchId')
  @ApiOperation({ summary: 'Get inventory by product and branch' })
  @ApiResponse({ status: 200, description: 'Inventory found' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  findByProductAndBranch(
    @Req() req: Request,
    @Param('productId', ParseIntPipe) productId: number,
    @Param('branchId', ParseIntPipe) branchId: number,
  ) {
    const user = req.user;
    return this.inventoryService.findByProductAndBranch(productId, branchId, user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get inventory by ID' })
  @ApiResponse({ status: 200, description: 'Inventory found' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.inventoryService.findOne(id);
  }

  @Get(':id/transactions')
  @ApiOperation({ summary: 'Get transaction history for inventory' })
  getTransactionHistory(@Req() req: Request, @Param('id', ParseIntPipe) id: number) {
    const user = req.user;
    return this.inventoryService.getTransactionHistory(id, user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update inventory record' })
  @ApiResponse({ status: 200, description: 'Inventory updated successfully' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ) {
    return this.inventoryService.update(id, updateInventoryDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete inventory record' })
  @ApiResponse({ status: 200, description: 'Inventory deleted successfully' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.inventoryService.remove(id);
  }

  @Post('stock-in')
  @ApiOperation({ summary: 'Add stock to inventory' })
  @ApiResponse({ status: 200, description: 'Stock added successfully' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  stockIn(@Req() req: Request, @Body() createTransactionDto: CreateInventoryTransactionDto) {
    const user = req.user;
    return this.inventoryService.stockIn(createTransactionDto, user);
  }

  @Post('stock-out')
  @ApiOperation({ summary: 'Remove stock from inventory' })
  @ApiResponse({ status: 200, description: 'Stock removed successfully' })
  @ApiResponse({ status: 400, description: 'Insufficient stock' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  stockOut(@Req() req: Request, @Body() createTransactionDto: CreateInventoryTransactionDto) {
    const user = req.user;
    return this.inventoryService.stockOut(createTransactionDto, user);
  }

  @Post('adjust')
  @ApiOperation({ summary: 'Adjust inventory stock' })
  @ApiResponse({ status: 200, description: 'Stock adjusted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid adjustment' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  adjustStock(@Req() req: Request, @Body() stockAdjustmentDto: StockAdjustmentDto) {
    const user = req.user;
    return this.inventoryService.adjustStock(stockAdjustmentDto, user);
  }

  @Post('transfer')
  @ApiOperation({ summary: 'Transfer stock between branches' })
  @ApiResponse({ status: 200, description: 'Stock transferred successfully' })
  @ApiResponse({ status: 400, description: 'Insufficient stock for transfer' })
  @ApiResponse({ status: 404, description: 'Inventory not found' })
  transferStock(@Req() req: Request, @Body() stockTransferDto: StockTransferDto) {
    const user = req.user;
    return this.inventoryService.transferStock(stockTransferDto, user);
  }

  @Post(':id/reserve')
  @ApiOperation({ summary: 'Reserve stock' })
  @ApiResponse({ status: 200, description: 'Stock reserved successfully' })
  @ApiResponse({ status: 400, description: 'Insufficient available stock' })
  reserveStock(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('quantity') quantity: number,
  ) {
    const user = req.user;
    return this.inventoryService.reserveStock(id, quantity, user);
  }

  @Post(':id/release')
  @ApiOperation({ summary: 'Release reserved stock' })
  @ApiResponse({ status: 200, description: 'Reserved stock released successfully' })
  @ApiResponse({ status: 400, description: 'Cannot release more than reserved' })
  releaseReservedStock(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('quantity') quantity: number,
  ) {
    const user = req.user;
    return this.inventoryService.releaseReservedStock(id, quantity, user);
  }
}
