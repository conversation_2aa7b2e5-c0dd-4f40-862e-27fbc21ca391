import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';
import { DateTime } from 'luxon';

import { Inventory } from './entities/inventory.entity';
import { InventoryTransaction, InventoryTransactionType } from './entities/inventory-transaction.entity';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { CreateInventoryTransactionDto } from './dto/create-inventory-transaction.dto';
import { StockAdjustmentDto, StockTransferDto } from './dto/stock-adjustment.dto';
import { Product } from '../product/entities/product.entity';
import { Branch } from '../branch/entities/branch.entity';

export const INVENTORY_PAGINATION_CONFIG: PaginateConfig<Inventory> = {
  sortableColumns: ['id', 'currentStock', 'minStock', 'lastUpdated'],
  searchableColumns: ['product.name', 'product.code', 'branch.name', 'location'],
  defaultSortBy: [['lastUpdated', 'DESC']],
  relations: ['product', 'branch', 'store'],
};

export const INVENTORY_TRANSACTION_PAGINATION_CONFIG: PaginateConfig<InventoryTransaction> = {
  sortableColumns: ['id', 'transactionDate', 'quantity', 'type'],
  searchableColumns: ['transactionNo', 'referenceNo', 'supplier', 'notes'],
  defaultSortBy: [['transactionDate', 'DESC']],
  relations: ['inventory', 'inventory.product', 'inventory.branch', 'createdBy'],
};

@Injectable()
export class InventoryService {
  constructor(
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private transactionRepository: Repository<InventoryTransaction>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Inventory>> {
    return paginate(query, this.inventoryRepository, {
      ...INVENTORY_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async transactionDatatables(query: PaginateQuery, user: any): Promise<Paginated<InventoryTransaction>> {
    return paginate(query, this.transactionRepository, {
      ...INVENTORY_TRANSACTION_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { inventory: { store: { id: user.storeId } } } : {}),
      },
    });
  }

  async create(createInventoryDto: CreateInventoryDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if inventory already exists for this product-branch combination
      const existingInventory = await this.inventoryRepository.findOne({
        where: {
          product: { id: createInventoryDto.productId },
          branch: { id: createInventoryDto.branchId },
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (existingInventory) {
        throw new ConflictException('Inventory already exists for this product-branch combination');
      }

      // Verify product and branch exist and belong to the user's store
      const product = await this.productRepository.findOne({
        where: {
          id: createInventoryDto.productId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!product) {
        throw new NotFoundException('Product not found');
      }

      const branch = await this.branchRepository.findOne({
        where: {
          id: createInventoryDto.branchId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!branch) {
        throw new NotFoundException('Branch not found');
      }

      // Calculate available stock
      const availableStock = (createInventoryDto.currentStock || 0) - (createInventoryDto.reservedStock || 0);

      // Create inventory record
      const inventory = this.inventoryRepository.create({
        ...createInventoryDto,
        availableStock,
        lastUpdated: new Date(),
        product: { id: createInventoryDto.productId },
        branch: { id: createInventoryDto.branchId },
        store: { id: user?.storeId },
      });

      const savedInventory = await queryRunner.manager.save(inventory);

      // Create initial transaction if there's initial stock
      if (createInventoryDto.currentStock && createInventoryDto.currentStock > 0) {
        await this.createTransaction(
          {
            inventoryId: savedInventory.id,
            type: InventoryTransactionType.INITIAL,
            quantity: createInventoryDto.currentStock,
            unitCost: createInventoryDto.averageCost,
            notes: 'Initial stock entry',
          },
          user,
          queryRunner,
        );
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedInventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(user: any, filters?: any): Promise<Inventory[]> {
    const where: any = {
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.productId) {
      where.product = { id: filters.productId };
    }

    if (filters?.branchId) {
      where.branch = { id: filters.branchId };
    }

    if (filters?.lowStock) {
      // This will be handled in the query builder for complex conditions
    }

    return this.inventoryRepository.find({
      where,
      relations: ['product', 'branch', 'store'],
      order: { lastUpdated: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: { id },
      relations: ['product', 'branch', 'store', 'transactions'],
    });

    if (!inventory) {
      throw new NotFoundException('Inventory not found');
    }

    return inventory;
  }

  async findByProductAndBranch(productId: number, branchId: number, user: any): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: {
        product: { id: productId },
        branch: { id: branchId },
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
      relations: ['product', 'branch', 'store'],
    });

    if (!inventory) {
      throw new NotFoundException('Inventory not found for this product-branch combination');
    }

    return inventory;
  }

  async update(id: number, updateInventoryDto: UpdateInventoryDto): Promise<Inventory> {
    const inventory = await this.findOne(id);

    // Calculate new available stock if current or reserved stock is being updated
    let availableStock = inventory.availableStock;
    if (updateInventoryDto.currentStock !== undefined || updateInventoryDto.reservedStock !== undefined) {
      const newCurrentStock = updateInventoryDto.currentStock ?? inventory.currentStock;
      const newReservedStock = updateInventoryDto.reservedStock ?? inventory.reservedStock;
      availableStock = newCurrentStock - newReservedStock;
    }

    await this.inventoryRepository.update(id, {
      ...updateInventoryDto,
      availableStock,
      lastUpdated: new Date(),
    });

    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const inventory = await this.findOne(id);

    const nowStr = DateTime.now().toLocal().toString();
    inventory.location = `${nowStr}-${inventory.location || ''}`;
    await inventory.save();

    await this.inventoryRepository.softRemove(inventory);
  }

  async adjustStock(stockAdjustmentDto: StockAdjustmentDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(stockAdjustmentDto.inventoryId);
      const oldQuantity = inventory.currentStock;
      const newQuantity = stockAdjustmentDto.newQuantity;
      const difference = newQuantity - oldQuantity;

      if (difference === 0) {
        throw new BadRequestException('No adjustment needed - quantities are the same');
      }

      // Update inventory
      inventory.currentStock = newQuantity;
      inventory.availableStock = newQuantity - inventory.reservedStock;
      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // Create adjustment transaction
      await this.createTransaction(
        {
          inventoryId: inventory.id,
          type: InventoryTransactionType.ADJUSTMENT,
          quantity: Math.abs(difference),
          referenceNo: stockAdjustmentDto.referenceNo,
          notes: stockAdjustmentDto.notes,
          reason: stockAdjustmentDto.reason || (difference > 0 ? 'Stock increase' : 'Stock decrease'),
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async transferStock(stockTransferDto: StockTransferDto, user: any): Promise<{ from: Inventory; to: Inventory }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const fromInventory = await this.findOne(stockTransferDto.fromInventoryId);
      const toInventory = await this.findOne(stockTransferDto.toInventoryId);

      if (fromInventory.availableStock < stockTransferDto.quantity) {
        throw new BadRequestException('Insufficient stock for transfer');
      }

      // Update source inventory
      fromInventory.currentStock -= stockTransferDto.quantity;
      fromInventory.availableStock -= stockTransferDto.quantity;
      fromInventory.lastUpdated = new Date();
      await queryRunner.manager.save(fromInventory);

      // Update destination inventory
      toInventory.currentStock += stockTransferDto.quantity;
      toInventory.availableStock += stockTransferDto.quantity;
      toInventory.lastUpdated = new Date();
      await queryRunner.manager.save(toInventory);

      // Create transfer out transaction
      await this.createTransaction(
        {
          inventoryId: fromInventory.id,
          type: InventoryTransactionType.TRANSFER,
          quantity: -stockTransferDto.quantity,
          referenceNo: stockTransferDto.referenceNo,
          notes: `Transfer to ${toInventory.branch.name} - ${stockTransferDto.notes || ''}`,
        },
        user,
        queryRunner,
      );

      // Create transfer in transaction
      await this.createTransaction(
        {
          inventoryId: toInventory.id,
          type: InventoryTransactionType.TRANSFER,
          quantity: stockTransferDto.quantity,
          referenceNo: stockTransferDto.referenceNo,
          notes: `Transfer from ${fromInventory.branch.name} - ${stockTransferDto.notes || ''}`,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return {
        from: await this.findOne(fromInventory.id),
        to: await this.findOne(toInventory.id),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async stockIn(createTransactionDto: CreateInventoryTransactionDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(createTransactionDto.inventoryId);

      // Update inventory
      inventory.currentStock += createTransactionDto.quantity;
      inventory.availableStock += createTransactionDto.quantity;

      // Update costs if provided
      if (createTransactionDto.unitCost) {
        inventory.lastCost = createTransactionDto.unitCost;

        // Calculate new average cost using weighted average
        if (inventory.averageCost && inventory.currentStock > createTransactionDto.quantity) {
          const oldValue = (inventory.currentStock - createTransactionDto.quantity) * inventory.averageCost;
          const newValue = createTransactionDto.quantity * createTransactionDto.unitCost;
          inventory.averageCost = (oldValue + newValue) / inventory.currentStock;
        } else {
          inventory.averageCost = createTransactionDto.unitCost;
        }
      }

      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // Create transaction
      await this.createTransaction(
        {
          ...createTransactionDto,
          type: InventoryTransactionType.STOCK_IN,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async stockOut(createTransactionDto: CreateInventoryTransactionDto, user: any): Promise<Inventory> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await this.findOne(createTransactionDto.inventoryId);

      if (inventory.availableStock < createTransactionDto.quantity) {
        throw new BadRequestException('Insufficient stock available');
      }

      // Update inventory
      inventory.currentStock -= createTransactionDto.quantity;
      inventory.availableStock -= createTransactionDto.quantity;
      inventory.lastUpdated = new Date();
      await queryRunner.manager.save(inventory);

      // Create transaction
      await this.createTransaction(
        {
          ...createTransactionDto,
          type: InventoryTransactionType.STOCK_OUT,
        },
        user,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      return this.findOne(inventory.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getLowStockItems(user: any): Promise<Inventory[]> {
    return this.inventoryRepository
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('inventory.store', 'store')
      .where('inventory.currentStock <= inventory.minStock')
      .andWhere('inventory.active = :active', { active: true })
      .andWhere(user?.storeId ? 'inventory.store.id = :storeId' : '1=1', { storeId: user?.storeId })
      .orderBy('inventory.currentStock', 'ASC')
      .getMany();
  }

  async getInventoryReport(user: any, filters?: any): Promise<any> {
    const queryBuilder = this.inventoryRepository
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('inventory.store', 'store')
      .where(user?.storeId ? 'inventory.store.id = :storeId' : '1=1', { storeId: user?.storeId });

    if (filters?.branchId) {
      queryBuilder.andWhere('inventory.branch.id = :branchId', { branchId: filters.branchId });
    }

    if (filters?.categoryId) {
      queryBuilder.andWhere('product.category.id = :categoryId', { categoryId: filters.categoryId });
    }

    const inventories = await queryBuilder.getMany();

    const summary = {
      totalItems: inventories.length,
      totalStockValue: inventories.reduce((sum, inv) => sum + inv.stockValue, 0),
      lowStockItems: inventories.filter(inv => inv.isLowStock).length,
      outOfStockItems: inventories.filter(inv => inv.currentStock <= 0).length,
      totalCurrentStock: inventories.reduce((sum, inv) => sum + inv.currentStock, 0),
    };

    return {
      summary,
      inventories,
    };
  }

  private async createTransaction(
    createTransactionDto: CreateInventoryTransactionDto,
    user: any,
    queryRunner?: QueryRunner,
  ): Promise<InventoryTransaction> {
    const manager = queryRunner ? queryRunner.manager : this.dataSource.manager;

    const inventory = await manager.findOne(Inventory, {
      where: { id: createTransactionDto.inventoryId },
    });

    if (!inventory) {
      throw new NotFoundException('Inventory not found');
    }

    // Generate transaction number
    const transactionNo = await this.generateTransactionNumber();

    const transaction = this.transactionRepository.create({
      ...createTransactionDto,
      transactionNo,
      stockBefore: inventory.currentStock - (createTransactionDto.type === InventoryTransactionType.STOCK_IN ? createTransactionDto.quantity : 0),
      stockAfter: inventory.currentStock + (createTransactionDto.type === InventoryTransactionType.STOCK_OUT ? -createTransactionDto.quantity : 0),
      totalCost: createTransactionDto.unitCost ? createTransactionDto.quantity * createTransactionDto.unitCost : null,
      transactionDate: new Date(),
      inventory: { id: createTransactionDto.inventoryId },
      createdBy: user ? { id: user.id } : null,
    });

    return manager.save(transaction);
  }

  private async generateTransactionNumber(): Promise<string> {
    const today = DateTime.now().toFormat('yyyyMMdd');
    const count = await this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.transactionNo LIKE :pattern', { pattern: `TXN${today}%` })
      .getCount();

    return `TXN${today}${(count + 1).toString().padStart(4, '0')}`;
  }

  async getTransactionHistory(inventoryId: number, user: any): Promise<InventoryTransaction[]> {
    return this.transactionRepository.find({
      where: {
        inventory: {
          id: inventoryId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      },
      relations: ['inventory', 'inventory.product', 'inventory.branch', 'createdBy'],
      order: { transactionDate: 'DESC' },
    });
  }

  async reserveStock(inventoryId: number, quantity: number, user: any): Promise<Inventory> {
    const inventory = await this.findOne(inventoryId);

    if (inventory.availableStock < quantity) {
      throw new BadRequestException('Insufficient available stock for reservation');
    }

    inventory.reservedStock += quantity;
    inventory.availableStock -= quantity;
    inventory.lastUpdated = new Date();

    await this.inventoryRepository.save(inventory);
    return inventory;
  }

  async releaseReservedStock(inventoryId: number, quantity: number, user: any): Promise<Inventory> {
    const inventory = await this.findOne(inventoryId);

    if (inventory.reservedStock < quantity) {
      throw new BadRequestException('Cannot release more stock than reserved');
    }

    inventory.reservedStock -= quantity;
    inventory.availableStock += quantity;
    inventory.lastUpdated = new Date();

    await this.inventoryRepository.save(inventory);
    return inventory;
  }
}
