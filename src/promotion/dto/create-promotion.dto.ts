import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Not<PERSON>mpty,
  IsN<PERSON>ber,
  ValidateIf,
} from 'class-validator';
import { PromotionType } from '../entities/promotion.entity';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePromotionDto {
  @IsNotEmpty()
  readonly code: string;

  @IsNotEmpty()
  readonly name: string;

  readonly detail?: string;

  @IsNotEmpty()
  @IsNumber()
  readonly amount: number;

  @IsNumber()
  @IsNotEmpty()
  @ValidateIf((o) => o.type == PromotionType.POINT)
  readonly point: number;

  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate?: Date;

  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate?: Date;

  @IsNotEmpty()
  readonly type: PromotionType;

  @IsNotEmpty()
  readonly isMember: boolean;

  @IsNotEmpty()
  readonly isActive: boolean;
}
