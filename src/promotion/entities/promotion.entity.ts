import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity } from 'typeorm';

export enum PromotionType {
  VOUCHER = 'voucher',
  POINT = 'point',
}

@Entity()
export class Promotion extends CustomBaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column()
  detail: string;

  @Column()
  amount: number;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date' })
  endDate: Date;

  @Column({ type: 'enum', enum: PromotionType })
  type: PromotionType;

  @Column({ name: 'is_member' })
  isMember: boolean;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;
}
