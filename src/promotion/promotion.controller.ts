import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Put,
} from '@nestjs/common';
import {
  PROMOTION_PAGINATION_CONFIG,
  PromotionService,
} from './promotion.service';
import { CreatePromotionDto } from './dto/create-promotion.dto';
import { UpdatePromotionDto } from './dto/update-promotion.dto';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';

@Controller('promotion')
@ApiTags('โปรโมชั่น')
@Auth()
export class PromotionController {
  constructor(private readonly promotionService: PromotionService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PROMOTION_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.promotionService.datatables(query);
  }

  @Post()
  @ApiConsumes('application/x-www-form-urlencoded')
  @ApiConsumes('application/json')
  create(@Body() createPromotionDto: CreatePromotionDto) {
    return this.promotionService.create(createPromotionDto);
  }

  @Get()
  findAll() {
    return this.promotionService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.promotionService.findOne(+id);
  }

  @Put(':id')
  update(
    @Param('id') id: string,
    @Body() updatePromotionDto: UpdatePromotionDto,
  ) {
    return this.promotionService.update(+id, updatePromotionDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.promotionService.remove(+id);
  }
}
